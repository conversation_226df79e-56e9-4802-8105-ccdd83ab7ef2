import 'dart:convert';

import 'package:com_common/common.dart';
import 'package:com_common/core/auth/auth_manager.dart';
import 'package:com_common/core/common_shared_preferences.dart';
import 'package:com_common/core/firebase_util/firebase_utils.dart';
import 'package:com_common/core/firebase_util/local_notification_service.dart';
import 'package:flutter/material.dart';
import 'package:myapp/data/model/local_notification/local_notification.dart';
import 'package:myapp/data/model/notification_response/notification_model.dart';
import 'package:myapp/di/dependency_injection.dart' as di;
import 'package:myapp/presentation/cubit/change_student/change_student_cubit.dart';
import 'package:myapp/presentation/cubit/notification/notification_count_cubit.dart';
import 'package:myapp/presentation/cubit/notification/notification_list_cubit.dart';
import 'package:myapp/presentation/cubit/signin/signin_cubit.dart';
import 'package:myapp/presentation/screen/article/article_screen.dart';
import 'package:myapp/presentation/screen/bus/bus_screen.dart';
import 'package:myapp/presentation/screen/calendar_event/event_screen2.dart';
import 'package:myapp/presentation/screen/chat/room_list/chat_room_screen.dart';
import 'package:myapp/presentation/screen/civilized/civilized_screen.dart';
import 'package:myapp/presentation/screen/exercise/exercise_screen.dart';
import 'package:myapp/presentation/screen/improvement_report/improvement_report_screen.dart';
import 'package:myapp/presentation/screen/improvement_report/monthly_report_screen.dart';
import 'package:myapp/presentation/screen/request_absent_detail/request_absent_detail_screen.dart';
import 'package:myapp/presentation/screen/request_support/request_support_detail_screen.dart';
import 'package:myapp/presentation/screen/signin/signin_screen.dart';
import 'package:myapp/presentation/screen/survey/survey_detail_screen.dart';
import 'package:myapp/route/navigation_service.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

Future<void> setupFCM() async {
  FirebaseUtils.setUpFCMMessage(
    _handleTokenChange,
    onSelectNotification: _handleSelectNotification,
    onReceiveMessage: _handleReceiveMessage,
    onMessageOpenedApp: _handleMessageOpenedApp,
    onReceiveBackgroundMessage: onBackgroundMessage,
  );
}

Future<void> onBackgroundMessage(RemoteMessage message) async {
  debugPrint('onBackgroundMessage - $message');
  _reloadData();
  if (message.notification == null) {
    _handleReceiveMessage(message);
  }
}

Future<void> _handleTokenChange(String? token) async {
  debugPrint('_handleTokenChange = $token');
  final appSharedPreferences = di.sl<AppCommonSharedPreferences>();
  appSharedPreferences.setFCMToken(token ?? '');
  // await registerDeviceWithToken(token);
}

_handleReceiveMessage(RemoteMessage message) {
  _reloadData();
  final localNotification = message.toLocalModel();
  final title = localNotification.notificationTitle;
  final content = localNotification.notificationContent;
  final payload = jsonEncode(localNotification);
  LocalNotificationService.showNotification(
      title: title, content: content, payload: payload);
}

_handleSelectNotification(NotificationResponse response) {
  debugPrint('select notification, payload= ${response.payload}');
  if (response.payload != null) {
    final message = LocalNotification.fromJson(jsonDecode(response.payload!));
    debugPrint('select notification, message= $message data= ${message.data}');
    _handleNavigateNotification(message);
  }
}

_handleMessageOpenedApp(RemoteMessage message) {
  debugPrint('message opened app, message= $message');
  _handleNavigateNotification(message.toLocalModel());
}

Future<void> _handleNavigateNotification(LocalNotification notification) async {
  final authToken = di.sl<AuthManager>();
  final navigatorKey = di.sl.get<NavigationService>().navigatorKey;
  final isLogin = await authToken.isLogin();
  final model = notification.data?.notificationData?.toModel();
  final appSharedPreferences = di.sl<AppCommonSharedPreferences>();

  if (navigatorKey.currentState == null) {
    return;
  }

  if (isLogin) {
    // Change student if need

    if (BlocProvider.of<ChangeStudentCubit>(navigatorKey.currentState!.context)
            .currentStudent
            ?.id !=
        model?.studentId) {
      final user =
          BlocProvider.of<SignInCubit>(navigatorKey.currentState!.context)
              .storedResponse;
      final willChangeStudent = user?.students
          ?.firstWhereOrNull((element) => element.id == model?.studentId);
      if (user != null && willChangeStudent != null) {
        BlocProvider.of<ChangeStudentCubit>(navigatorKey.currentState!.context)
            .change(
                fullName: user.fullName ?? '',
                id: user.id,
                student: willChangeStudent);
      }
    }

    String screenName = '';
    Map<String, Object?>? arguments;

    switch (model?.type) {
      case NotificationDataType.news:
        screenName = ArticleScreen.routeName;
        arguments = {
          'articleId': model?.objectId ?? 0,
          'isCommentAction': false
        };
        break;

      case NotificationDataType.absent:
        screenName = AbsentRequestDetailScreen.routeName;
        arguments = {
          'id': model?.objectId ?? 0,
        };

        break;

      case NotificationDataType.survey:
        screenName = SurveyDetailScreen.routeName;
        arguments = {
          'id': model?.objectId ?? 0,
        };

        break;
      case NotificationDataType.score:
        screenName = ExerciseListScreen.routeName;

        break;
      case NotificationDataType.comment:
        screenName = EventScreen2.routeName;
        arguments = {
          'date': notification.data?.sentTime,
          'commentId': model?.objectId
        };

        break;
      case NotificationDataType.subjectComment:
        screenName = EventScreen2.routeName;
        arguments = {
          'date': notification.data?.sentTime,
          'commentId': model?.objectId
        };
        break;
      case NotificationDataType.attendance:
        screenName = EventScreen2.routeName;
        arguments = {
          'date': notification.data?.sentTime,
          'commentId': model?.objectId
        };
        break;
      case NotificationDataType.subjectAttendance:
        screenName = EventScreen2.routeName;
        arguments = {
          'date': notification.data?.sentTime,
          'commentId': model?.objectId
        };
        break;
      case NotificationDataType.regularlyReport:
        screenName = MonthlyReportScreen.routeName;
        break;

      case NotificationDataType.assignment:
        screenName = EventScreen2.routeName;
        arguments = {
          'date': notification.data?.sentTime,
          'commentId': model?.objectId
        };
        break;

      case NotificationDataType.improvementReport:
        screenName = ImprovementReportScreen.routeName;
        arguments = {'year': model?.namHoc};

        break;

      case NotificationDataType.bus:
        screenName = BusScreen.routeName;
        arguments = {
          'date': notification.data?.sentTime,
        };
        break;

      case NotificationDataType.civilized:
        screenName = CivilizedScreen.routeName;
        break;
      case NotificationDataType.support:
        screenName = RequestSupportDetailScreen.routeName;
        arguments = {'id': model?.objectId ?? 0};
        break;
      default:
        break;
    }
    if (screenName.isNotEmpty == true) {
      Navigator.pushNamed(navigatorKey.currentState!.context, screenName,
          arguments: arguments);
    }

    // debugPrint(
    //     'I WILL NAVIGATE TO SCREEN WITH DATA: ${notification.toString()}');
    // BlocProvider.of<NotificationCountCubit>(navigatorKey.currentState!.context)
    //     .get(
    //         studentId: ((BlocProvider.of<ChangeStudentCubit>(
    //                             navigatorKey.currentState!.context)
    //                         .currentStudent ??
    //                     BlocProvider.of<SignInCubit>(
    //                             navigatorKey.currentState!.context)
    //                         .storedResponse
    //                         ?.students
    //                         ?.firstOrNull))
    //                 ?.id ??
    //             0);
  } else {
    Navigator.pushNamedAndRemoveUntil(
      navigatorKey.currentState!.context,
      SignInScreen.routeName,
      ModalRoute.withName('/'),
    );
  }
}

Future<void> _reloadData() async {
  final authToken = di.sl<AuthManager>();
  final navigatorKey = di.sl.get<NavigationService>().navigatorKey;
  final isLogin = await authToken.isLogin();

  if (isLogin && navigatorKey.currentState?.context != null) {
    BlocProvider.of<NotificationCountCubit>(navigatorKey.currentState!.context)
        .get(
            studentId: ((BlocProvider.of<ChangeStudentCubit>(
                                navigatorKey.currentState!.context)
                            .currentStudent ??
                        BlocProvider.of<SignInCubit>(
                                navigatorKey.currentState!.context)
                            .storedResponse
                            ?.students
                            ?.firstOrNull))
                    ?.id ??
                0);

    BlocProvider.of<NotificationListCubit>(navigatorKey.currentState!.context)
        .get(
            studentId: (BlocProvider.of<ChangeStudentCubit>(
                                navigatorKey.currentState!.context)
                            .currentStudent ??
                        BlocProvider.of<SignInCubit>(
                                navigatorKey.currentState!.context)
                            .storedResponse
                            ?.students
                            ?.firstOrNull)
                    ?.id ??
                0);
  }
}
