import 'dart:async';
import 'dart:developer';

import 'package:com_common/core/environment_configuration.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
// import 'package:flutter_stetho/flutter_stetho.dart';
import 'package:myapp/app/app.dart';
import 'package:com_common/core/common_shared_preferences.dart';
import 'package:com_common/core/firebase_util/firebase_utils.dart';
import 'package:com_common/core/store/shared_preferences_impl.dart';
import 'package:com_uikit/uikit.dart';
import 'package:myapp/app/app_environment.dart';
import 'package:myapp/app/notification_utils.dart';
import 'package:myapp/di/dependency_injection.dart' as di;
import 'package:flutter_driver/driver_extension.dart';

typedef InitConfig = Future Function(BuildContext context);

class AppEnvironmentConfig {
  late InitConfig initConfig;
  late AppCommonSharedPreferences appSDKSharedPreferences;
  late String environment;

  static final _instance = AppEnvironmentConfig._internal();

  AppEnvironmentConfig._internal();

  static AppEnvironmentConfig getInstance() {
    return _instance;
  }

  String environmentToString() {
    return environment.split('.').last;
  }

  bool isAutomation() {
    return environmentToString().contains('auto');
  }
}

void runAppWithEnvironment(String environment) {
  // Stetho.initialize();
  runZonedGuarded<Future<void>>(() async {
    WidgetsFlutterBinding.ensureInitialized();

    //init SP
    final appSharedPreferences = AppCommonSharedPreferences(sharedPreferences: CommonPreferencesImpl());

    // init DI
    AppEnvironment env = environment.stringToAppEnvironment();
    await initEnvironment(env);
    await di.registerDI();
    // init Environment config
    Future init(BuildContext context) async {
      // await Stetho.initialize();
      log("Stetho initialized");
      ThemeProvider.instance.checkInit(
        forceNew: true,
        currentTheme: AppTheme.local,
        primaryColor: HexColor.fromHex('#1D6482'),
        secondaryColor: HexColor.fromHex('#FAAF28'),
        // fontFamily: FontFamily.sFProText,
      );
      if (environment.stringToEnvironment().isAutomation()) {
        enableFlutterDriverExtension();
      } else {
        await FirebaseUtils.requestNotificationPermissions();

        await setupFCM();
      }
    }

    AppEnvironmentConfig.getInstance().appSDKSharedPreferences = appSharedPreferences;
    AppEnvironmentConfig.getInstance().initConfig = init;
    AppEnvironmentConfig.getInstance().environment = environment;

    runApp(const MyApp());
  }, (error, stack) {
    log("error: $error");
    log("stack: $stack");
    try {
      FirebaseCrashlytics.instance.recordError(error, stack);
    } catch (e) {
      log("Error firebase crashlytics please iggnore: $e");
    }
  });

  // runApp(const MyApp());
}

Future<void> initEnvironment(AppEnvironment environment) async {
  // await common.initSDK(environment);
  AppConfigModel.instance.init(
    config: environment.config(),
  );
}
