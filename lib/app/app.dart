import 'dart:developer';
import 'package:com_uikit/data/theme_response.dart';
import 'package:com_uikit/uikit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:myapp/app/router.dart';
import 'package:myapp/l10n/gen/app_localizations.dart';
import 'package:myapp/l10n/l10n.dart';
import 'package:myapp/presentation/cubit/absent/absent_request_list_cubit.dart';
import 'package:myapp/presentation/cubit/bookmark/bookmark_cubit.dart';
import 'package:myapp/presentation/cubit/bookmark/bookmark_delete_cubit.dart';
import 'package:myapp/presentation/cubit/change_student/change_student_cubit.dart';
import 'package:myapp/presentation/cubit/config/config_cubit.dart';
import 'package:myapp/presentation/cubit/notification/notification_count_cubit.dart';
import 'package:myapp/presentation/cubit/otp/otp_cubit.dart';
import 'package:myapp/presentation/cubit/otp/verify_otp_cubit.dart';
import 'package:myapp/presentation/cubit/prescription/get_prescription_list_cubit.dart';
import 'package:myapp/presentation/cubit/schedule/schedule_cubit.dart';
import 'package:myapp/presentation/cubit/signin/signin_cubit.dart';
import 'package:myapp/presentation/cubit/support/get_support_list_cubit.dart';
import 'package:myapp/presentation/cubit/uniform/uniform_cart_cubit.dart';
import 'package:myapp/presentation/cubit/uniform/uniform_update_cart_cubit.dart';
import 'package:myapp/presentation/cubit/user_profile/user_profile_cubit.dart';
import 'package:myapp/presentation/screen/splash/splash_screen.dart';
import 'package:myapp/route/navigation_service.dart';
import 'package:com_common/common.dart';
import 'package:com_common/core/common_shared_preferences.dart';
import 'package:com_common/l10n/gen/common_localizations.dart';
import 'package:com_common/presentation/locale/locale_cubit.dart';
import 'package:myapp/di/dependency_injection.dart' as di;
import 'package:com_common/presentation/locale/locale_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/single_child_widget.dart';

/// The Widget that configures your application.
class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    getSaveLocale();
    super.initState();
  }

  getSaveLocale() async {
    var locale = await di.sl.get<AppCommonSharedPreferences>().getLocale();
    di.sl.get<LocaleCubit>().setLocale(Locale(locale ?? 'vi'));
  }

  @override
  Widget build(BuildContext context) {
    // Glue the SettingsController to the MaterialApp.
    //
    // The AnimatedBuilder Widget listens to the SettingsController for changes.
    // Whenever the user updates their settings, the MaterialApp is rebuilt.

    const localizationDelegates = [
      AppLocalizations.delegate,
      CommonLocalizations.delegate,
      GlobalMaterialLocalizations.delegate,
      GlobalWidgetsLocalizations.delegate,
      GlobalCupertinoLocalizations.delegate,
    ];
    return MultiBlocProvider(
      providers: _appProviders,
      child: MultiBlocListener(
        listeners: _blocListeners,
        child: ScreenUtilInit(
          useInheritedMediaQuery: true,
          designSize: const Size(428, 926),
          builder: (context, widget) => FutureBuilder(
              future: ThemeProvider.instance.initDefaultTheme(
                  newTheme: ThemeResponse(color: AppColor(primaryColor: '#1D6482', secondaryColor: '#FAAF28'))),
              builder: (context, _) {
                return MaterialApp(
                    theme: ThemeProvider.instance.themeData,
                    debugShowCheckedModeBanner: false,
                    navigatorKey: di.sl.get<NavigationService>().navigatorKey,
                    restorationScopeId: 'app',
                    localizationsDelegates: localizationDelegates,
                    locale: context.watch<LocaleCubit>().appLocale,
                    supportedLocales: L10n.all,
                    home: const SplashScreen(),
                    onGenerateRoute: generateRoute);
              }),
        ),
      ),
    );
  }

  List<BlocListener> get _blocListeners {
    return [
      BlocListener<LocaleCubit, LocaleState>(
        listener: (context, state) {
          log('LocaleCubit');
        },
      ),
    ];
  }

  List<SingleChildWidget> get _appProviders {
    return [
      BlocProvider(
        create: (_) => di.sl<LocaleCubit>(),
      ),
      BlocProvider(
        create: (_) => di.sl<SignInCubit>(),
      ),
      BlocProvider(
        create: (_) => di.sl<UserProfileCubit>(),
      ),
      BlocProvider(
        create: (_) => di.sl<GetConfigCubit>(),
      ),
      BlocProvider(
        create: (_) => di.sl<OTPCubit>(),
      ),
      BlocProvider(
        create: (_) => di.sl<VerifyOTPCubit>(),
      ),
      BlocProvider(
        create: (_) => di.sl<ChangeStudentCubit>(),
      ),
      BlocProvider(
        create: (_) => di.sl<ScheduleCubit>(),
      ),
      BlocProvider(
        create: (_) => di.sl<BookmarkCubit>(),
      ),
      BlocProvider(
        create: (_) => di.sl<DeleteBookmarkCubit>(),
      ),
      BlocProvider(
        create: (_) => di.sl<GetPrescriptionListCubit>(),
      ),
      BlocProvider(
        create: (_) => di.sl<AbsentRequestListCubit>(),
      ),
      BlocProvider(
        create: (_) => di.sl<GetSupportListCubit>(),
      ),
      BlocProvider(
        create: (_) => di.sl<NotificationCountCubit>(),
      ),
      BlocProvider(
        create: (_) => di.sl<UniformCartCubit>(),
      ),
      BlocProvider(
        create: (_) => di.sl<UniformCartUpdateCubit>(),
      ),
    ];
  }
}
