import 'package:flutter/material.dart';
import 'package:myapp/data/model/bus_service_response/bus_service_response.dart';
import 'package:myapp/data/model/prescription/prescription_response.dart';
import 'package:myapp/data/model/response/uniform_cart_response/cart_model.dart';
import 'package:myapp/presentation/screen/birthday/birthday_screen.dart';
import 'package:myapp/presentation/screen/chat/chat_detail/chat_screen.dart';
import 'package:myapp/presentation/screen/chat/chat_detail/group_chat_information_screen.dart';
import 'package:myapp/presentation/screen/chat/room_list/chat_room_screen.dart';
import 'package:myapp/presentation/screen/extracurricular/club_online_payment_screen.dart';
import 'package:myapp/presentation/screen/extracurricular/club_select_payment_method_screen.dart';
import 'package:myapp/presentation/screen/extracurricular/extracurricular_cart_screen.dart';
import 'package:myapp/presentation/screen/extracurricular/extracurricular_cart_transfer_payment_screen.dart';
import 'package:myapp/presentation/screen/extracurricular/extracurricular_detail_screen.dart';
import 'package:myapp/presentation/screen/extracurricular/extracurricular_list_screen.dart';
import 'package:myapp/presentation/screen/guest_home/guest_article_screen.dart';
import 'package:myapp/presentation/screen/guest_home/guest_main_screen.dart';
import 'package:myapp/presentation/screen/profile/avatar/avatar_screen.dart';
import 'package:myapp/presentation/screen/profile/avatar/capture/capture_screen.dart';
import 'package:myapp/presentation/screen/request_absent/absense_request_list_screen.dart';
import 'package:myapp/presentation/screen/article/article_screen.dart';
import 'package:myapp/presentation/screen/bus/bus_register_screen.dart';
import 'package:myapp/presentation/screen/bus/bus_screen.dart';
import 'package:myapp/presentation/screen/calendar_event/event_screen.dart';
import 'package:myapp/presentation/screen/calendar_event/event_screen2.dart';
import 'package:myapp/presentation/screen/change_password/change_password_screen.dart';
import 'package:myapp/presentation/screen/civilized/civilized_screen.dart';
import 'package:myapp/presentation/screen/contact/contact_detail/user_profile_screen.dart';
import 'package:myapp/presentation/screen/contact/contact_screen.dart';
import 'package:myapp/presentation/screen/course/course_detail_screen.dart';
import 'package:myapp/presentation/screen/course/course_screen.dart';
import 'package:myapp/presentation/screen/daily_event/daily_assignment_screen.dart';
import 'package:myapp/presentation/screen/exercise/exercise_detail_screen.dart';
import 'package:myapp/presentation/screen/exercise/exercise_screen.dart';
import 'package:myapp/presentation/screen/improvement_report/improvement_report_screen.dart';
import 'package:myapp/presentation/screen/meal_menu/meal_menu_screen.dart';
import 'package:myapp/presentation/screen/notifications/notifications_screen.dart';
import 'package:myapp/presentation/screen/prescription/create_prescription/create_prescription_screen.dart';
import 'package:myapp/presentation/screen/prescription/prescription_screen.dart';
// import 'package:myapp/presentation/screen/profile/setting2_screen.dart';
// import 'package:myapp/presentation/screen/profile/setting3_screen.dart';
import 'package:myapp/presentation/screen/register_subject/register_subject_list_screen.dart';
import 'package:myapp/presentation/screen/register_subject/register_subject_screen.dart';
import 'package:myapp/presentation/screen/request_absent/absent_model.dart';
import 'package:myapp/presentation/screen/request_absent/create_absent_request_screen.dart';
import 'package:myapp/presentation/screen/daily_event/daily_event_screen.dart';
import 'package:myapp/presentation/screen/forgot_password/forgot_screen.dart';
import 'package:myapp/presentation/screen/home/<USER>';
import 'package:myapp/presentation/screen/main_tab/main_home_screen.dart';
import 'package:myapp/presentation/screen/onboard/onboard_screen.dart';
import 'package:myapp/presentation/screen/request_absent_detail/request_absent_detail_screen.dart';
import 'package:myapp/presentation/screen/request_support/request_support_detail_screen.dart';
import 'package:myapp/presentation/screen/request_support/request_support_list_screen.dart';
import 'package:myapp/presentation/screen/request_support/request_support_screen.dart';
import 'package:myapp/presentation/screen/score/score_screen.dart';
import 'package:myapp/presentation/screen/service_and_payment/service_and_payment_screen.dart';
import 'package:myapp/presentation/screen/signin/signin_screen.dart';
import 'package:myapp/presentation/screen/splash/splash_screen.dart';
import 'package:myapp/presentation/screen/student/student_screen.dart';
import 'package:myapp/presentation/screen/student_profile/student_profile_screen.dart';
import 'package:myapp/presentation/screen/survey/survey_detail_screen.dart';
import 'package:myapp/presentation/screen/survey/survey_list_screen.dart';
import 'package:myapp/presentation/screen/tuition_fee/payment_method_screen.dart';
import 'package:myapp/presentation/screen/tuition_fee/tuition_fee_detail_screen.dart';
import 'package:myapp/presentation/screen/tuition_fee/tuition_fee_online_payment_screen.dart';
import 'package:myapp/presentation/screen/tuition_fee/tuition_fee_screen.dart';
import 'package:myapp/presentation/screen/tuition_fee/tuition_fee_transfer_payment_screen.dart';
import 'package:myapp/presentation/screen/uniform/uniform_cart_detail_screen.dart';
import 'package:myapp/presentation/screen/uniform/uniform_cart_transfer_payment_screen.dart';
import 'package:myapp/presentation/screen/uniform/uniform_item_detail_screen.dart';
import 'package:myapp/presentation/screen/uniform/uniform_online_payment_screen.dart';
import 'package:myapp/presentation/screen/uniform/uniform_order_list_screen.dart';
import 'package:myapp/presentation/screen/uniform/uniform_screen.dart';
import 'package:myapp/presentation/screen/uniform/uniform_select_payment_method_screen.dart';
import 'package:myapp/presentation/screen/uniform/uniform_set_combo_screen.dart';
import 'package:myapp/route/router.dart';

Route<dynamic>? generateRoute(RouteSettings settings) {
  var routingData = getRoutingData(settings.name);
  debugPrint('>>>>>> ${routingData.route}-----');
  var data = settings.arguments as dynamic;
  switch (routingData.route) {
    case SplashScreen.routeName:
      return getPageRoute(const SplashScreen(), settings);
    case SignInScreen.routeName:
      return getPageRoute(const SignInScreen(), settings, isFade: true);
    case ChangePasswordScreen.routeName:
      return getPageRoute(const ChangePasswordScreen(), settings,
          isFade: false);
    case ForgotPasswordScreen.routeName:
      dynamic phoneNumber = data?['phoneNumber'];
      return getPageRoute(
          ForgotPasswordScreen(
            phoneNumber: phoneNumber,
          ),
          settings,
          isFade: false);
    case MainHomeScreen.routeName:
      return getPageRoute(const MainHomeScreen(), settings, isFade: true);
    case StudentScreen.routeName:
      return getPageRoute(const StudentScreen(), settings, isFade: true);
    case OnboardScreen.routeName:
      return getPageRoute(const OnboardScreen(), settings, isFade: true);
    case ArticleScreen.routeName:
      int articleId = data?['articleId'] ?? 0;
      bool isCommentAction = data?['isCommentAction'] ?? false;
      return getPageRoute(
          ArticleScreen(articleId: articleId, isCommentAction: isCommentAction),
          settings,
          isFade: false,
          isFromBottom: true);
    case GuestArticleScreen.routeName:
      int articleId = data?['articleId'] ?? 0;
      bool isCommentAction = data?['isCommentAction'] ?? false;
      return getPageRoute(
          GuestArticleScreen(
              articleId: articleId, isCommentAction: isCommentAction),
          settings,
          isFade: false);

    case AbsentRequestDetailScreen.routeName:
      dynamic id = data?['id'];
      return getPageRoute(
          AbsentRequestDetailScreen(
            id: id,
          ),
          settings,
          isFade: false);
    case ArticleListScreen.routeName:
      String? keyword = data?['keyword'];
      bool isBookmarked = data?['isBookmarked'] ?? false;
      return getPageRoute(
          ArticleListScreen(keyword: keyword, isBookmarked: isBookmarked),
          settings,
          isFade: false);
    case EventScreen.routeName:
      return getPageRoute(const EventScreen(), settings, isFade: true);
    case EventScreen2.routeName:
      DateTime? date = data?['date'];
      int? commentId = data?['commentId'];
      return getPageRoute(
          EventScreen2(selectedDay: date, commentId: commentId), settings,
          isFade: true);
    case RegisterSubjectScreen.routeName:
      dynamic id = data?['id'] as int?;
      dynamic isExpired = data?['isExpired'] as bool?;
      return getPageRoute(
          RegisterSubjectScreen(
            timeId: id,
            isExpired: isExpired,
          ),
          settings,
          isFade: true);
    case RegisterSubjectListScreen.routeName:
      return getPageRoute(const RegisterSubjectListScreen(), settings,
          isFade: true);

    // case Setting2Screen.routeName:
    //   return getPageRoute(const Setting2Screen(), settings, isFade: true);
    // case Setting3Screen.routeName:
    //   return getPageRoute(const Setting3Screen(), settings, isFade: true);

    case DailyEventScreen.routeName:
      int index = data?['index'];
      DateTime? date = data?['date'] as DateTime?;
      return getPageRoute(
          DailyEventScreen(
            index: index,
            defaultDate: date,
          ),
          settings,
          isFade: true);
    case CreateAbsentRequestScreen.routeName:
      dynamic detail = data?['detail'];
      dynamic bus = data?['bus'];
      AbsentType? type = data?['type'];
      return getPageRoute(
          CreateAbsentRequestScreen(
            data: detail,
            bus: bus,
            type: type,
          ),
          settings,
          isFade: true);
    case StudentProfileScreen.routeName:
      return getPageRoute(const StudentProfileScreen(), settings,
          isFade: false);
    case BirthdayScreen.routeName:
      return getPageRoute(const BirthdayScreen(), settings, isFade: false);
    case ContactScreen.routeName:
      int index = data?['index'] ?? 0;
      return getPageRoute(
          ContactScreen(
            defaultIndex: index,
          ),
          settings,
          isFade: false);
    case PrescriptionScreen.routeName:
      return getPageRoute(const PrescriptionScreen(), settings, isFade: false);
    case CourseScreen.routeName:
      return getPageRoute(const CourseScreen(), settings, isFade: false);
    case CourseDetailScreen.routeName:
      return getPageRoute(const CourseDetailScreen(), settings, isFade: false);
    // case LessionPlayScreen.routeName:
    //   return getPageRoute(const LessionPlayScreen(), settings, isFade: false);
    case RequestSupportScreen.routeName:
      return getPageRoute(const RequestSupportScreen(), settings,
          isFade: false);
    case RequestSupportListScreen.routeName:
      return getPageRoute(const RequestSupportListScreen(), settings,
          isFade: false);
    case ExerciseListScreen.routeName:
      return getPageRoute(const ExerciseListScreen(), settings, isFade: false);
    case ScoreScreen.routeName:
      return getPageRoute(const ScoreScreen(), settings, isFade: false);
    case CivilizedScreen.routeName:
      return getPageRoute(const CivilizedScreen(), settings, isFade: false);
    case ImprovementReportScreen.routeName:
      String? year = data?['year'] as String?;
      return getPageRoute(
          ImprovementReportScreen(
            year: year,
          ),
          settings,
          isFade: false);
    case BusScreen.routeName:
      DateTime? date = data?['date'] as DateTime?;
      int? tabIndex = data?['tabIndex'] as int?;
      return getPageRoute(
          BusScreen(
            date: date,
            tabIndex: tabIndex,
          ),
          settings,
          isFade: false);
    case ServiceAndPaymentScreen.routeName:
      return getPageRoute(const ServiceAndPaymentScreen(), settings,
          isFade: false);

    case AbsenseRequestListScreen.routeName:
      return getPageRoute(const AbsenseRequestListScreen(), settings,
          isFade: false);
    case SurveyListScreen.routeName:
      return getPageRoute(const SurveyListScreen(), settings, isFade: false);
    case NotificationsScreen.routeName:
      return getPageRoute(const NotificationsScreen(), settings, isFade: false);
    case SurveyDetailScreen.routeName:
      int? id = data?['id'];
      String? title = data?['title'];
      return getPageRoute(
          SurveyDetailScreen(
            id: id ?? 0,
            title: title,
          ),
          settings,
          isFade: false);
    case DailyAssignmentScreen.routeName:
      int? index = data?['index'];
      DateTime? date = data?['date'] as DateTime?;
      return getPageRoute(
          DailyAssignmentScreen(
            index: index ?? 0,
            date: date,
          ),
          settings,
          isFade: false);
    case CreatePrescriptionScreen.routeName:
      PrescriptionResponse? prescriptionData = data?['data'];
      return getPageRoute(
          CreatePrescriptionScreen(
            data: prescriptionData,
          ),
          settings,
          isFade: false);
    case BusRegisterScreen.routeName:
      BusServiceResponse? detail = data?['detail'];
      return getPageRoute(
          BusRegisterScreen(
            model: detail,
          ),
          settings,
          isFade: false);
    case ExerciseDetailScreen.routeName:
      int id = data?['id'];
      return getPageRoute(
          ExerciseDetailScreen(
            id: id,
          ),
          settings,
          isFade: true);
    case RequestSupportDetailScreen.routeName:
      dynamic id = data?['id'];
      return getPageRoute(
          RequestSupportDetailScreen(
            id: id,
          ),
          settings,
          isFade: false);
    case UserProfileScreen.routeName:
      dynamic profile = data?['profile'];
      return getPageRoute(
          UserProfileScreen(
            profile: profile,
          ),
          settings,
          isFade: false);

    case UniformCartDetailScreen.routeName:
      List<CartModel>? model = data?['detail'];
      int? index = data?['index'];
      return getPageRoute(
          UniformCartDetailScreen(
            carts: model,
            defaultIndex: index,
          ),
          settings,
          isFade: false,
          isFromBottom: true);
    case UniformScreen.routeName:
      return getPageRoute(const UniformScreen(), settings, isFade: true);
    case UniformItemDetailScreen.routeName:
      String tag = data?['tag'];
      String name = data?['name'];
      int id = data?['id'];
      return getPageRoute(
          UniformItemDetailScreen(
            tag: tag,
            productId: id,
            name: name,
          ),
          settings,
          isFade: true);
    case UniformOnlinePaymentScreen.routeName:
      dynamic url = data?['url'];
      dynamic orderId = data?['orderId'];
      return getPageRoute(
          UniformOnlinePaymentScreen(
            url: url,
            orderId: orderId,
          ),
          settings,
          isFade: false,
          isFromBottom: false);

    case ClubOnlinePaymentScreen.routeName:
      dynamic url = data?['url'];
      return getPageRoute(ClubOnlinePaymentScreen(url: url), settings,
          isFade: false, isFromBottom: false);

    case TuitionFeeOnlinePaymentScreen.routeName:
      dynamic url = data?['url'];
      return getPageRoute(TuitionFeeOnlinePaymentScreen(url: url), settings,
          isFade: false, isFromBottom: false);
    case UniformOrderListScreen.routeName:
      dynamic objectId = data?['objectId'];
      return getPageRoute(
          UniformOrderListScreen(
            objectIdForRouting: objectId,
          ),
          settings,
          isFade: false);
    case UniformCartTransferConfirmScreen.routeName:
      int id = data?['id'] as int? ?? 0;
      String code = data?['code'] as String? ?? '';
      double price = data?['price'] as double? ?? 0.0;
      return getPageRoute(
          UniformCartTransferConfirmScreen(
            orderId: id,
            orderCode: code,
            totalPrice: price,
          ),
          settings,
          isFade: true);
    case UniformSetComboScreen.routeName:
      int id = data?['id'];
      String name = data?['name'];
      bool isStandardCombo = data?['isStandardCombo'];
      return getPageRoute(
          UniformSetComboScreen(
              setId: id, name: name, isStandardCombo: isStandardCombo),
          settings,
          isFade: true);
    case UniformSelectPaymentMethodScreen.routeName:
      int id = data?['id'];
      String code = data?['code'];
      bool isFromCart = data?['isFromCart'];
      double price = data?['price'];
      return getPageRoute(
          UniformSelectPaymentMethodScreen(
            orderId: id,
            orderCode: code,
            totalPrice: price,
            isFromCart: isFromCart,
          ),
          settings,
          isFade: true);
    case GuestMainScreen.routeName:
      return getPageRoute(const GuestMainScreen(), settings, isFade: false);
    case ChatRoomScreen.routeName:
      return getPageRoute(const ChatRoomScreen(), settings, isFade: false);
    case ChatScreen.routeName:
      dynamic room = data?['room'];
      return getPageRoute(
          ChatScreen(
            room: room,
          ),
          settings,
          isFade: false,
          isFromBottom: true);
    case GroupChatInformationScreen.routeName:
      dynamic room = data?['room'];
      return getPageRoute(
          GroupChatInformationScreen(
            room: room,
          ),
          settings,
          isFade: false,
          isFromBottom: true);
    case ExtracurricularListScreen.routeName:
      return getPageRoute(const ExtracurricularListScreen(), settings,
          isFade: false);
    case MealMenuScreen.routeName:
      return getPageRoute(const MealMenuScreen(), settings, isFade: false);
    case ExtracurricularDetailScreen.routeName:
      bool isDetail = data?['isDetail'];
      int id = data?['id'];
      return getPageRoute(
          ExtracurricularDetailScreen(
            isDetail: isDetail,
            id: id,
          ),
          settings,
          isFade: false);

    case ExtracurricularCartScreen.routeName:
      dynamic order = data?['order'];
      return getPageRoute(
          ExtracurricularCartScreen(
            order: order,
          ),
          settings,
          isFade: false);
    case ExtracurricularClubCartTransferConfirmScreen.routeName:
      int orderId = data?['orderId'] as int? ?? 0;
      String orderCode = data?['orderCode'] as String? ?? '';
      dynamic price = data?['price'];
      return getPageRoute(
          ExtracurricularClubCartTransferConfirmScreen(
            orderId: orderId,
            orderCode: orderCode,
            totalPrice: price,
          ),
          settings,
          isFade: false);
    case CropImageScreen.routeName:
      String? path = data?['path'];
      return getPageRoute(
          CropImageScreen(
            initialFile: path,
          ),
          settings,
          isFade: false);
    case TuitionFeeScreen.routeName:
      return getPageRoute(const TuitionFeeScreen(), settings, isFade: false);
    case TuitionFeeDetailScreen.routeName:
      dynamic fee = data?['fee'];
      return getPageRoute(
          TuitionFeeDetailScreen(
            data: fee,
          ),
          settings,
          isFade: false);

    case SelectPaymentMethodScreen.routeName:
      int methodId = data?['methodId'];
      String year = data?['year'];

      double price = data?['price'];
      return getPageRoute(
          SelectPaymentMethodScreen(
            methodId: methodId,
            year: year,
            totalPrice: price,
          ),
          settings,
          isFade: true);
    case TuitionFeeTransferConfirmScreen.routeName:
      int orderId = data?['orderId'] as int? ?? 0;
      String year = data?['year'] as String? ?? '';
      double price = data?['price'] as double? ?? 0.0;
      return getPageRoute(
          TuitionFeeTransferConfirmScreen(
            orderId: orderId,
            year: year,
            totalPrice: price,
          ),
          settings,
          isFade: true);
    case ClubSelectPaymentMethodScreen.routeName:
      int id = data?['id'];
      String code = data?['code'];
      bool isFromCart = data?['isFromCart'];
      double price = data?['price'];
      return getPageRoute(
          ClubSelectPaymentMethodScreen(
            orderId: id,
            orderCode: code,
            totalPrice: price,
            isFromCart: isFromCart,
          ),
          settings,
          isFade: true);
    case CaptureScreen.routeName:
      return getPageRoute(const CaptureScreen(), settings, isFade: false);
    default:
      return getPageRoute(const SplashScreen(), settings);
  }
}
