// import 'package:com_common/constant/enum_value.dart';

// enum RemoteNotificationType {
//   ARTICLE_NEW,
//   ARTICLE_COMMENT,
//   DAILY_SCHEDULE,
//   DAILY_COMMENT,
//   DAILY_COMMENT_REPLY,
//   DAILY_ATTENDANCE,
//   DAILY_ASSIGNMENT,
//   ABSENSE_REQUEST_APPROVE,
//   MEDICINE_REMINDER,
//   MEAL_MENU_UPDATE,
//   BUS_ATTENDANCE,
//   STUDENT_REPORT,
//   STUDENT_RESULT,
//   TUITION_FEE
// }

// final remoteNotificationTypeValues = EnumValues({
//   "ARTICLE_NEW": RemoteNotificationType.ARTICLE_NEW,
//   "ARTICLE_COMMENT": RemoteNotificationType.ARTICLE_COMMENT,
//   "DAILY_SCHEDULE": RemoteNotificationType.DAILY_SCHEDULE,
//   "DAILY_COMMENT": RemoteNotificationType.DAILY_COMMENT,
//   "DAILY_COMMENT_REPLY": RemoteNotificationType.DAILY_COMMENT_REPLY,
//   "DAILY_ATTENDANCE": RemoteNotificationType.DAILY_ATTENDANCE,
//   "DAILY_ASSIGNMENT": RemoteNotificationType.DAILY_ASSIGNMENT,
//   "ABSENSE_REQUEST_APPROVE": RemoteNotificationType.ABSENSE_REQUEST_APPROVE,
//   "MEDICINE_REMINDER": RemoteNotificationType.MEDICINE_REMINDER,
//   "MEAL_MENU_UPDATE": RemoteNotificationType.MEAL_MENU_UPDATE,
//   "BUS_ATTENDANCE": RemoteNotificationType.BUS_ATTENDANCE,
//   "STUDENT_REPORT": RemoteNotificationType.STUDENT_REPORT,
//   "STUDENT_RESULT": RemoteNotificationType.STUDENT_RESULT,
//   "TUITION_FEE": RemoteNotificationType.TUITION_FEE
// });
