import 'dart:developer';

import 'package:flutter/services.dart';
import 'package:myapp/constants/constant.dart';

enum AppEnvironment { dev, test, demo, production }

extension AppEnvironmentExtension on String {
  AppEnvironment stringToAppEnvironment() {
    return AppEnvironment.values
        .firstWhere((e) => e.toString() == 'AppEnvironment.$this', orElse: () => AppEnvironment.dev);
  }
}

extension AppEnvironmentString on AppEnvironment {
  String environmentToString() {
    return toString().split('.').last;
  }

  String get appName {
    return '[${environmentToString()}] Parent';
  }

  String get homeUrl {
    switch (this) {
      case AppEnvironment.test:
        return 'https://parent-app.edisonschools.edu.vn';

      case AppEnvironment.demo:
        return 'https://parent-app.edisonschools.edu.vn';

      case AppEnvironment.production:
        return 'https://parent-app.edisonschools.edu.vn';

      default:
        return 'https://parent-app.edisonschools.edu.vn';
    }
  }

  String get baseUrl {
    return '$homeUrl/api';
  }

  String get uniformPaymentCallback {
    return '$homeUrl/api/onepay/submit-result';
  }

  String descriptionEnvironment() {
    switch (this) {
      case AppEnvironment.test:
        return 'Môi trường phục vụ chị em QC';
      case AppEnvironment.demo:
        return 'Môi trường Demo cho Sale/Sếp';

      case AppEnvironment.production:
        return '[Không test] PRODUCTION';

      default:
        return 'Môi trường công nhân Dev';
    }
  }

  bool isAutomation() {
    return environmentToString().contains('auto');
  }

  Map<String, Map<String, Object>> get additionalConfig {
    switch (this) {
      default:
        return {
          'sslCertificatePinning': {
            'sha256': [
              '0F:BE:5E:4E:36:9D:2A:62:6F:CF:64:F2:ED:F1:CC:AE:DE:CF:A5:2B:85:A1:76:6F:B2:76:B7:44:53:CE:7A:C7', // finos.asia
              'DB:40:30:68:C5:FA:6B:6A:27:F1:02:8F:87:50:36:E3:F8:6C:CA:E2:2E:83:AB:DF:75:21:63:33:9E:9F:5F:8A' // virtserver mock
            ]
          },
          'theme': {'downloadResources': false}
        };
    }
  }

  Future<Map<String, Map<String, Object>>> configSSL(AppEnvironment environment) async {
    String fileName = 'Assets.res.certificatesDev';
    switch (environment) {
      case AppEnvironment.test:
        // fileName = Assets.res.certificatesStaging;
        break;
      case AppEnvironment.production:
        //  fileName = Assets.res.certificatesProd;
        break;
      default:
        //  fileName = Assets.res.certificatesDev;
        break;
    }
    try {
      //https://github.com/flutter/flutter/issues/65921
      String sha256 = await rootBundle.loadString('packages/$appPackageName/$fileName', cache: false);
      final sslCertificatePinning = {
        'sslCertificatePinning': {'sha256': sha256.split('\n')}
      };
      Map<String, Map<String, Object>> newConfig = {...additionalConfig, ...sslCertificatePinning};
      return newConfig;
    } catch (e) {
      log(e.toString());
    }
    return additionalConfig;
  }

  Map<String, dynamic> config() {
    return {
      'environment': environmentToString(),
      'baseUrl': baseUrl,
      'appName': appName,
      'uniformPaymentCallback': uniformPaymentCallback
    };
  }
}
