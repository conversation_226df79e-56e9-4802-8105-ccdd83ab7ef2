import 'dart:convert';
import 'dart:io';
import 'dart:isolate';
import 'package:camera/camera.dart';
import 'package:com_common/common_setup.dart';
import 'package:com_common/core/auth/auth_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:myapp/constants/file_utils.dart';
import 'package:myapp/constants/image_crop_utils.dart';
import 'package:myapp/data/service/support_service.dart';
import 'package:path_provider/path_provider.dart';

class MessageHandler {
  static const String success = 'success';
  static const String failure = 'failure';
}

Future<List<String>> _encodeBase64(
    List<String> arrImagePaths, String temporaryDirectoryPath) async {
  List<String> tempArr = [];
  for (var image in arrImagePaths) {
    var newFile = await File(image).compressFile(temporaryDirectoryPath);
    final bytes = newFile.readAsBytesSync();
    String img64 = base64Encode(bytes);
    tempArr.add('data:image/jpeg;base64,$img64');
  }
  return tempArr;
}

Future<List<File>> compressImageList(List<String> arrImagePaths) async {
  List<File> results = [];

  for (int i = 0; i < arrImagePaths.length; i++) {
    final tempDir = await getTemporaryDirectory();
    final newFile = await File(arrImagePaths[i])
        .compressFile(tempDir.path, fileName: 'image_$i');
    results.add(newFile);
  }
  return results;
}

Future<List<String>> _cropImages(double deviceWidth,
    String temporaryDirectoryPath, List<String> arrImagePaths) async {
  List<String> tempArr = [];
  for (var path in arrImagePaths) {
    final newFile = await ImageUtils.cropSquare(
        deviceWidth, path, '$temporaryDirectoryPath/${XFile(path).name}', null);
    tempArr.add(newFile.path);
  }
  return tempArr;
}

Future<Map<String, dynamic>> createUploadDocumentParams(
    BuildContext context, List<String> arrImagePaths) async {
  return {};
  // EKYCConfig ekycConfig = Ekyc.getInstance().config;
  // final _authSaleman = GetIt.instance<AuthManager>(
  //     instanceName: TokenType.salemanToken.toString());
  // return {
  //   'baseUrl': AppConfigModel.instance.baseUrl,
  //   'images': _arrImagePaths,
  //   'documentType': ekycConfig.documentType,
  //   'documentName': ekycConfig.documentName,
  //   'documentId': ekycConfig.documentID,
  //   'documentCode': ekycConfig.documentCode,
  //   'consumerId': ekycConfig.consumerID,
  //   'authorization': await _authSaleman.getToken(),
  //   'idToken': await _authSaleman.getIdToken(),
  //   'borrowerIdToken': await _authSaleman.getBorrowerIdToken(),
  //   'deviceWidth': MediaQuery.of(context).size.width,
  //   'isAutomation': isAutomation,
  //   'temporaryDirectoryPath': (await getTemporaryDirectory()).path,
  // };
}

@pragma('vm:entry-point')
void doUploadFile(List<Object> arguments) async {
  SendPort sendPort = arguments[0] as SendPort;
  Map params = arguments[1] as Map;
  try {
    String imgPath = params['image'];
    String baseUrl = params['baseUrl'];
    String token = params['token'];
    final dio = _createDio(baseUrl, token, 'vi');
    final tempDir = await getTemporaryDirectory();
    final newFile = await File(imgPath).compressFile(tempDir.path);
    final response = await SupportService(dio).uploadFile(image: newFile);
    // debugPrint('RESULT = ${response.data.data.toString()}}');
    final result = response.data.data;
    debugPrint(
        'RESULT = ${result?['is_success'].toString()}. LINK: ${result?['url']}');
    return sendPort.send(result?['url']);
  } catch (error) {
    sendPort.send(null);
  }
}

@pragma('vm:entry-point')
void doRequestSupport(List<Object> arguments) async {
  SendPort sendPort = arguments[0] as SendPort;
  Map params = arguments[1] as Map;
  try {
    // final auth = di.sl<AuthManager>();

    List<String> arrImagePaths = params['images'];
    int groupId = params['groupId'];
    int studentId = params['studentId'];
    String reason = params['reason'];
    String baseUrl = params['baseUrl'];
    String token = params['token'];
    // String lang = params['lang'];
    // String authorization = await auth.getToken() ?? '';
    final dio = _createDio(baseUrl, token, 'vi');
    List<File> images = await compressImageList(arrImagePaths);
    final response = await SupportService(dio).requestSupport(
        groupId: groupId, studentId: studentId, reason: reason, images: images);

    debugPrint(jsonEncode(response.data.data).toString());
    return sendPort.send(MessageHandler.success);
  } catch (error) {
    debugPrint(error.toString());
    sendPort.send(error.toString());
  }
}

Dio _createDio(
  String baseUrl,
  String authorization,
  String lang,
) {
  final dio = createCoreDio(baseUrl);
  dio.interceptors.add(CustomAuthInterceptor(authorization, lang));
  return dio;
}

class CustomAuthInterceptor extends InterceptorsWrapper {
  final String token, lang;

  CustomAuthInterceptor(
    this.token,
    this.lang,
  );

  @override
  Future<void> onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    try {
      options.headers.addAll(setupAuthHeaders(token, lang));
      super.onRequest(options, handler);
    } catch (e) {
      handler.reject(DioException(
          requestOptions: options,
          type: DioExceptionType.connectionError,
          error: e));
    }
  }
}
