import 'package:flutter/material.dart';
import 'package:myapp/app/app_config.dart';

const String appType = 'parent';
const String appPackageName = 'mobileapp';
const String baseUrlName = 'baseUrl';
const String configurationKey = 'Configurations';
const String appleId = '6748092476';
const String playStoreId = 'com.edison.parent';
const int expectedData = 512 * 512;

const int kAnimationAppearDuration = 200;
const int kAnimationTutorialDuration = 400;
const int kLongAnimationAppearDuration = 1500;

const String articleHeaderTag = 'articleHeaderTag';

const String isMaintenanceKey = 'is_under_maintenance';
const String isEnableOnlinePayment = 'isEnableOnlinePayment';
const String forceUpdateVersionKey = 'version_force_update';
const String isEnableBiometricKey = 'enable_biometric';
const String isEnableCommentKey = 'enable_comment';
const String isEnableForgotPassword = 'enable_forgot';
const String isEnableCarousel = 'enable_carousel';

final isAutomation = AppEnvironmentConfig.getInstance().isAutomation();
const String otpBlockTime = 'otp_block_time';
const String otpExpirationTime = 'otp_expiration';
const String otpMaxResent = 'otp_max_resent';
const String otpMaxVerification = 'otp_max_verification';

const double kProfileItemHeight = 125;
const int kPageSize = 20;

const backgroundColorLightBlue = '#ECF5FF';
const backgroundColorLightOrange = '#FFF8E5';

const String kHotlineInternationalElementarySchoolOffice = 'International Elementary School';
const String kHotlineInternationalMiddleAndHighSchool = 'International Middle and High School';
const String kHotlineBilingualElementarySchool = 'Bilingual Elementary School';
const String kHotlineBilingualMiddleAndHighSchool = 'BIlingual Middle and High School';
const String kHotlineMealsAndShuttleBus = 'Meals, Shuttle bus';
const String kHotlineUniforms = 'Uniforms';
const String kHotlineASA = 'ECAs (Clubs & ASAs)';
const String kHotlineKoreanAdmissionOfficer = 'Korean Admission Officer';
const String kHotlineAdmissionOfficer = 'Admissions Officer';
const String kHotlineAccountant = 'Chief Accountant';

const avatarColors = [
  Color(0xffff6767),
  Color(0xff66e0da),
  Color(0xfff5a2d9),
  Color(0xfff0c722),
  Color(0xff6a85e5),
  Color(0xfffd9a6f),
  Color(0xff92db6e),
  Color(0xff73b8e5),
  Color(0xfffd7590),
  Color(0xffc78ae5),
];
