import 'dart:developer';
import 'dart:io';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:myapp/constants/constant.dart';

extension FileExtension on File {
  Future<File> compressFile(String temporaryDirectoryPath,
      {String? fileName}) async {
    var newFile = this;
    var rawData = await readAsBytes();
    if (rawData.lengthInBytes > expectedData) {
      int ratio = expectedData * 100 ~/ rawData.lengthInBytes;
      log('COMPRESS IMAGE WITH RATIO $ratio');
      rawData =
          await FlutterImageCompress.compressWithList(rawData, quality: ratio);
      newFile = await File('$temporaryDirectoryPath/${fileName ?? 'image'}.png')
          .create();
      newFile.writeAsBytesSync(rawData);
    }
    return newFile;
  }
}
