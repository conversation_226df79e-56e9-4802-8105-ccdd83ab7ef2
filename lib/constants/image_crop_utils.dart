import 'dart:io';
import 'dart:math';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;
import 'package:myapp/constants/constant.dart';
import 'package:path_provider/path_provider.dart';

class ImageUtils {
  static Future<File> cropSquare(double deviceWith, String srcFilePath,
      String destFilePath, double? ratio) async {
    var bytes = await File(srcFilePath).readAsBytes();
    img.Image src =
        img.decodeImage(bytes) ?? img.Image(width: 100, height: 100);

    double ratioImg = ratio ?? 1.47;
    double height = src.width / ratioImg;

    var cropSizeHeight = height;

    int offsetX = (deviceWith - src.width) ~/ 2;
    var cropSizeWidth = max(deviceWith - offsetX * 2, src.width);
    int offsetY = (src.height - min(height, src.height)) ~/ 2;

    img.Image destImage = img.copyCrop(
      src,
      x: offsetX,
      y: offsetY,
      width: cropSizeWidth.floor(),
      height: cropSizeHeight.floor(),
    );
    var jpg = img.encodeJpg(destImage);
    var newFile = File(destFilePath).writeAsBytes(jpg);
    return newFile;
  }

  static Future<File> imageToFile(ByteData bytes) async {
    final tempPath = (await getTemporaryDirectory()).path;
    final file = File('$tempPath/profile.png');
    await file.writeAsBytes(
        bytes.buffer.asUint8List(bytes.offsetInBytes, bytes.lengthInBytes));
    return file;
  }

  static Future<File> getPassportAutomationImage() async {
    final tempDir = await getTemporaryDirectory();
    const path = 'passport_automation.png';
    final byteData =
        await rootBundle.load('packages/$appPackageName/assets/icons/$path');
    final file = File('${tempDir.path}/$path');
    if (file.existsSync()) {
      return file;
    }
    await file.writeAsBytes(byteData.buffer
        .asUint8List(byteData.offsetInBytes, byteData.lengthInBytes));
    return file;
  }
}
