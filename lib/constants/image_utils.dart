import 'dart:typed_data';

import 'package:flutter/painting.dart';
import 'package:image/image.dart';
import 'package:camera/camera.dart';

extension ImageToData on Image {
  Uint8List toUint8List() {
    PngEncoder pngEncoder = PngEncoder();
    List<int> png = pngEncoder.encode(this);
    return Uint8List.fromList(png);
  }
}

extension CameraImageExtension on CameraImage {
  Image? cropImage(
      CameraLensDirection dir, Rect faceRect, double croppedBoundary) {
    double x, y, w, h;
    x = faceRect.left - (faceRect.width / 2);
    x = x > 0 ? x : 0;

    y = faceRect.top - (faceRect.height / 2);
    y = y > 0 ? y : 0;
    w = 2 * faceRect.width;
    w = w > width.toDouble() ? width.toDouble() : w;
    h = 2 * faceRect.height;
    h = h > height.toDouble() ? height.toDouble() : h;
    return copyCrop(toImage(dir)!,
        x: x.round(), y: y.round(), width: w.round(), height: h.round());
  }

  Image? toImage(CameraLensDirection dir) {
    try {
      if (format.group == ImageFormatGroup.yuv420) {
        return convertToYUV420(dir);
      } else if (format.group == ImageFormatGroup.bgra8888) {
        return convertToBGRA8888(dir);
      }
      // ignore: empty_catches
    } catch (e) {}
    return null;
  }

  Image convertToBGRA8888(CameraLensDirection dir) {
    var img = Image.fromBytes(
      width: width,
      height: height,
      bytes: planes[0].bytes.buffer,
    );
    return img;
  }

  Image convertToYUV420(CameraLensDirection dir) {
    int width = this.width;
    int height = this.height;
    var img = Image(width: width, height: height);
    const int hexFF = 0xFF000000;
    final int uvyButtonStride = planes[1].bytesPerRow;
    final int? uvPixelStride = planes[1].bytesPerPixel;
    for (int x = 0; x < width; x++) {
      for (int y = 0; y < height; y++) {
        final int uvIndex = uvPixelStride! * (x / 2).floor() +
            uvyButtonStride * (y / 2).floor();
        final int index = y * width + x;
        final yp = planes[0].bytes[index];
        final up = planes[1].bytes[uvIndex];
        final vp = planes[2].bytes[uvIndex];
        int r = (yp + vp * 1436 / 1024 - 179).round().clamp(0, 255);
        int g = (yp - up * 46549 / 131072 + 44 - vp * 93604 / 131072 + 91)
            .round()
            .clamp(0, 255);
        int b = (yp + up * 1814 / 1024 - 227).round().clamp(0, 255);
        // img.data?[index] = hexFF | (b << 16) | (g << 8) | r;
        if (img.isBoundsSafe(height - y, x)) {
          img.setPixelRgba(height - y, x, r, g, b, hexFF);
        }
      }
    }
    var img1 = (dir == CameraLensDirection.front)
        ? copyRotate(img, angle: -90)
        : copyRotate(img, angle: 90);
    return img1;
  }
}
