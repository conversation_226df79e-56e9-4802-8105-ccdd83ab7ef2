import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:developer' as developer;

class PerformanceMonitor {
  static final Map<String, DateTime> _startTimes = {};
  static final Map<String, List<int>> _durations = {};

  static void startTimer(String operation) {
    if (kDebugMode) {
      _startTimes[operation] = DateTime.now();
      developer.log('⏱️ Started: $operation', name: 'Performance');
    }
  }

  static void endTimer(String operation) {
    if (kDebugMode && _startTimes.containsKey(operation)) {
      final duration = DateTime.now().difference(_startTimes[operation]!).inMilliseconds;
      _durations.putIfAbsent(operation, () => []).add(duration);
      
      developer.log('⏱️ Completed: $operation in ${duration}ms', name: 'Performance');
      
      // Log average if we have multiple measurements
      final durations = _durations[operation]!;
      if (durations.length > 1) {
        final avg = durations.reduce((a, b) => a + b) / durations.length;
        developer.log('📊 Average for $operation: ${avg.toStringAsFixed(1)}ms (${durations.length} samples)', name: 'Performance');
      }
      
      _startTimes.remove(operation);
    }
  }

  static void logMemoryUsage(String context) {
    if (kDebugMode) {
      developer.log('🧠 Memory check: $context', name: 'Performance');
    }
  }

  static void logWidgetBuild(String widgetName) {
    if (kDebugMode) {
      developer.log('🔨 Widget build: $widgetName', name: 'Performance');
    }
  }
}

class PerformanceWrapper extends StatefulWidget {
  final Widget child;
  final String name;

  const PerformanceWrapper({
    Key? key,
    required this.child,
    required this.name,
  }) : super(key: key);

  @override
  State<PerformanceWrapper> createState() => _PerformanceWrapperState();
}

class _PerformanceWrapperState extends State<PerformanceWrapper> {
  @override
  void initState() {
    super.initState();
    PerformanceMonitor.startTimer('${widget.name}_init');
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    PerformanceMonitor.endTimer('${widget.name}_init');
  }

  @override
  Widget build(BuildContext context) {
    PerformanceMonitor.logWidgetBuild(widget.name);
    return widget.child;
  }
}

class OptimizedBuilder extends StatelessWidget {
  final Widget Function(BuildContext context) builder;
  final String name;

  const OptimizedBuilder({
    Key? key,
    required this.builder,
    required this.name,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    PerformanceMonitor.startTimer('build_$name');
    final widget = builder(context);
    PerformanceMonitor.endTimer('build_$name');
    return widget;
  }
}

// Mixin for performance tracking
mixin PerformanceTrackingMixin<T extends StatefulWidget> on State<T> {
  String get performanceName => T.toString();

  @override
  void initState() {
    super.initState();
    PerformanceMonitor.startTimer('${performanceName}_init');
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    PerformanceMonitor.endTimer('${performanceName}_init');
  }

  @override
  Widget build(BuildContext context) {
    PerformanceMonitor.logWidgetBuild(performanceName);
    return buildWithPerformanceTracking(context);
  }

  Widget buildWithPerformanceTracking(BuildContext context);
}
