import 'package:flutter/material.dart';
import 'package:myapp/app/app_config.dart' as config;
import 'package:myapp/app/app_environment.dart';
import 'package:myapp/presentation/screen/environment/environment_selection_screen.dart';

void main() async {
  const argEnvironment = String.fromEnvironment("environment");
  final environment = argEnvironment.isEmpty ? AppEnvironment.dev.environmentToString() : argEnvironment.toLowerCase();
  if (environment == AppEnvironment.production.environmentToString()) {
    config.runAppWithEnvironment(environment);
  } else {
    runApp(const MaterialApp(
      debugShowCheckedModeBanner: false,
      home: EnvironmentSelectionScreen(),
    ));
  }
}
