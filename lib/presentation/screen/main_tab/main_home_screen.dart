import 'package:com_common/common.dart';
import 'package:com_uikit/uikit.dart';
import 'package:com_common/core/common_shared_preferences.dart';
import 'package:flutter/material.dart';
// import 'package:focused_menu/focused_menu.dart';
import 'package:myapp/constants/constant.dart';
import 'package:myapp/gen/assets.gen.dart';
import 'package:myapp/l10n/gen/app_localizations.dart';
import 'package:myapp/presentation/cubit/article/article_list_cubit.dart';
import 'package:myapp/presentation/cubit/article/article_reaction_cubit.dart';
import 'package:myapp/presentation/cubit/atendance/atendance_list_cubit.dart';
import 'package:myapp/presentation/cubit/bus/get_bus_history_cubit.dart';
import 'package:myapp/presentation/cubit/change_student/change_student_cubit.dart';
import 'package:myapp/presentation/cubit/module/module_cubit.dart';
import 'package:myapp/presentation/cubit/notification/mark_notification_readed_cubit.dart';
import 'package:myapp/presentation/cubit/notification/notification_count_cubit.dart';
import 'package:myapp/presentation/cubit/notification/notification_type_cubit.dart';
import 'package:myapp/presentation/cubit/schedule/new_calendar_cubit.dart';
import 'package:myapp/presentation/cubit/signin/signin_cubit.dart';
import 'package:myapp/presentation/cubit/signin/signout_cubit.dart';
import 'package:myapp/presentation/screen/bus/bus_screen.dart';
import 'package:myapp/presentation/screen/chat/room_list/chat_room_screen.dart';
import 'package:myapp/presentation/screen/home/<USER>';
import 'package:myapp/presentation/screen/notifications/notifications_screen.dart';
import 'package:myapp/presentation/screen/prescription/create_prescription/create_prescription_screen.dart';
import 'package:myapp/presentation/screen/prescription/prescription_screen.dart';
import 'package:myapp/presentation/screen/profile/setting_screen.dart';
import 'package:myapp/presentation/screen/request_absent/absense_request_list_screen.dart';
import 'package:myapp/presentation/screen/request_absent/create_absent_request_screen.dart';
import 'package:myapp/presentation/screen/request_support/request_support_list_screen.dart';
import 'package:myapp/presentation/screen/request_support/request_support_screen.dart';
import 'package:myapp/presentation/screen/student/student_screen.dart';
import 'package:myapp/di/dependency_injection.dart' as di;
import 'package:com_uikit/animated_bottom_navigation_bar/animated_bottom_navigation_bar.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';
import 'widgets/optimized_bottom_nav.dart';

class MainHomeScreen extends StatefulWidget {
  static const String routeName = '/main_home';

  const MainHomeScreen({super.key});

  @override
  State<MainHomeScreen> createState() => _MainHomeScreenState();
}

class _MainHomeScreenState extends State<MainHomeScreen> with TickerProviderStateMixin {
  late final animationController =
      AnimationController(duration: const Duration(milliseconds: kAnimationAppearDuration), vsync: this);
  final appSharedPreferences = di.sl<AppCommonSharedPreferences>();
  late PageController _pageController;
  late List<Widget> _listPage;
  int currentIndex = 0;

  late TutorialCoachMark tutorialCoachMark;

  GlobalKey keyButton = GlobalKey();
  GlobalKey keyButton1 = GlobalKey();

  GlobalKey keyBottomNavigation1 = GlobalKey();
  GlobalKey keyBottomNavigation2 = GlobalKey();
  GlobalKey keyBottomNavigation3 = GlobalKey();
  GlobalKey keyBottomNavigation4 = GlobalKey();
  GlobalKey keyBottomNavigation5 = GlobalKey();

  @override
  void initState() {
    _listPage = [
      HomeScreen(
        changeStudentKey: keyButton,
      ),
      const StudentScreen(
          // groupKey: keyButton1,
          ),
      const NotificationsScreen(),
      const SettingScreen(),
    ];
    _pageController = PageController(initialPage: currentIndex);
    BlocProvider.of<NotificationCountCubit>(context).get(
        studentId: ((BlocProvider.of<ChangeStudentCubit>(context).currentStudent ??
                    BlocProvider.of<SignInCubit>(context).storedResponse?.students?.firstOrNull))
                ?.id ??
            0);

    super.initState();
    // createTutorial();
  }

  createPopupMenu(BuildContext context) async {
    // PopupMenu.context = context;
    PopupMenu menu = PopupMenu(
        config: MenuConfig(
            animationController: animationController,
            type: MenuType.list,
            backgroundColor: lightGrey,
            itemColor: white,
            itemWidth: 240.w,
            itemHeight: 52
            // maxColumn: 1
            // maxColumn: 1,
            // arrowHeight: 10,
            ),
        context: context,
        items: [
          MenuItem(
              title: AppLocalizations.of(context)!.lbl_daily_event_absent,
              userInfo: CreateAbsentRequestScreen.routeName,
              image: SvgPicture.asset(Assets.icons.icCampsite),
              textStyle: ThemeProvider.instance.textStyleMed14),
          MenuItem(
              title: AppLocalizations.of(context)!.lbl_prescription_title,
              userInfo: CreatePrescriptionScreen.routeName,
              image: SvgPicture.asset(Assets.icons.icPill),
              textStyle: ThemeProvider.instance.textStyleMed14),
          // MenuItem(
          //     title: AppLocalizations.of(context)!.lbl_send_request_support,
          //     userInfo: RequestSupportScreen.routeName,
          //     image: SvgPicture.asset(
          //       Assets.icons.icPhone3,
          //     ),
          //     textStyle: ThemeProvider.instance.textStyleMed14),
        ],
        onClickMenu: (menu) {
          Navigator.of(context)
              .pushNamed(menu.menuUserInfo, arguments: {'data': null, 'bus': null, 'type': null}).then((value) {
            switch (menu.menuUserInfo) {
              case CreateAbsentRequestScreen.routeName:
                if (value == null) return;
                if (value == true) {
                  _safeNavigate(AbsenseRequestListScreen.routeName);
                } else if (value == false) {
                  _safeNavigate(BusScreen.routeName, arguments: {'date': DateTime.now(), 'tabIndex': 2});
                }
                break;

              case CreatePrescriptionScreen.routeName:
                if (value == null) return;
                _safeNavigate(PrescriptionScreen.routeName);
                break;

              case RequestSupportScreen.routeName:
                if (value == null) return;
                _safeNavigate(RequestSupportListScreen.routeName);
                break;
              default:
            }
          });
        },
        onDismiss: () => animationController.reset());
    final rect = getWidgetGlobalRect(keyBottomNavigation1);
    menu.show(
        rect: Rect.fromCenter(
            center: Offset(rect.center.dx, rect.center.dy - 4), width: rect.width, height: rect.height));
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _safeNavigate(String routeName, {Object? arguments}) {
    if (mounted) {
      Navigator.of(context).pushNamed(routeName, arguments: arguments);
    }
  }

  @override
  Widget build(BuildContext context) {
    final profile = BlocProvider.of<SignInCubit>(context).storedResponse;
    final student = profile?.students?.firstOrNull;
    return MultiBlocProvider(
        providers: [
          BlocProvider<NotificationTypeCubit>(
            create: (_) => di.sl<NotificationTypeCubit>(),
            lazy: false,
          ),
          BlocProvider<ArticleListCubit>(
            create: (_) => di.sl<ArticleListCubit>(),
            lazy: true,
          ),
          BlocProvider<NewCalendarCubit>(
            create: (_) => di.sl<NewCalendarCubit>(),
            lazy: true,
          ),
          BlocProvider<GetBusHistoryCubit>(
            create: (_) => di.sl<GetBusHistoryCubit>()
              ..get(
                  studentId: student?.id ?? 0,
                  fromTime: DateTime.now().millisecondsSinceEpoch ~/ 1000,
                  toTime: DateTime.now().millisecondsSinceEpoch ~/ 1000
                  // DateTime.now().millisecondsSinceEpoch ~/ 1000,
                  ),
          ),
          BlocProvider<ModuleCubit>(
            create: (_) => di.sl<ModuleCubit>()..get(studentId: student?.id ?? 0),
          ),
          BlocProvider<AtendanceListCubit>(
            create: (_) => di.sl<AtendanceListCubit>()
              ..get(studentId: student?.id ?? 0, date: DateTime.now().millisecondsSinceEpoch ~/ 1000),
          ),
          BlocProvider<MarkNotificationReadedCubit>(
            create: (_) => di.sl<MarkNotificationReadedCubit>(),
          ),
          BlocProvider<ArticleReactionCubit>(
            create: (_) => di.sl<ArticleReactionCubit>(),
          ),
          BlocProvider<SignOutCubit>(
            create: (_) => di.sl<SignOutCubit>(),
          ),
        ],
        child: Stack(
          children: [
            Scaffold(
              resizeToAvoidBottomInset: false,
              body: PageView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                children: _listPage,
              ),
              floatingActionButtonAnimator: AnimationNoScaling(),
              floatingActionButton: FloatingActionButton(
                key: keyBottomNavigation1,
                elevation: 0,
                backgroundColor: Colors.transparent,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(30.r),
                  child: Container(
                      color: Colors.transparent,
                      width: 150.r,
                      height: 150.r,
                      child: Image.asset(Assets.images.icLogo.path)),
                ),
                onPressed: () {
                  ///show popup menu with 3 items with position above the button
                  createPopupMenu(context);
                },
                //params
              ),
              floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
              bottomNavigationBar: _buildAnimatedBottomBar2(),
            ),
          ],
        ));
  }

  _buildAnimatedBottomBar2() {
    return AnimatedBottomNavigationBar.builder(
      itemCount: 4,
      tabBuilder: (int index, bool isActive) {
        // final color = isActive ? Colors.red : Colors.grey;
        return _itemList[index];
      },
      // backgroundColor: ThemeProvider.instance.primaryColor,

      activeIndex: currentIndex,
      splashColor: ThemeProvider.instance.primaryColor,
      // notchAndCornersAnimation: borderRadiusAnimation,
      splashSpeedInMilliseconds: 300,
      notchSmoothness: NotchSmoothness.defaultEdge,
      gapLocation: GapLocation.center,
      // leftCornerRadius: 32,
      // rightCornerRadius: 32,
      onTap: (index) => updatePage(index),
      // hideAnimationController: _hideBottomBarAnimationController,
      shadow: BoxShadow(
        offset: const Offset(0, 1),
        blurRadius: 12,
        spreadRadius: 0.5,
        color: lightGrey,
      ),
    );
  }

  updatePage(int page) {
    if (currentIndex != page) {
      setState(() {
        _pageController.jumpToPage(page);
        currentIndex = page;
      });
    }
  }

  List<Widget> get _itemList {
    /// array of bottom navigation items
    return [
      Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 34.h,
            child: Padding(
              padding: EdgeInsets.only(bottom: 4.h),
              child: SvgPicture.asset(
                Assets.icons.icBarHome,
                width: 20.w,
                height: 20.w,
                colorFilter: ColorFilter.mode(
                  0 == currentIndex ? ThemeProvider.instance.primaryColor : darkGrey,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          Text(AppLocalizations.of(context)!.lbl_home,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              softWrap: false,
              style: ThemeProvider.instance.textStyleMed14
                  .copyWith(color: currentIndex == 0 ? ThemeProvider.instance.primaryColor : darkGrey))
        ],
      ),
      Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 34.h,
            child: Padding(
              padding: EdgeInsets.only(bottom: 4.h),
              child: SvgPicture.asset(
                Assets.icons.icFunction,
                width: 20.w,
                height: 20.w,
                colorFilter: ColorFilter.mode(
                  1 == currentIndex ? ThemeProvider.instance.primaryColor : darkGrey,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          Text(AppLocalizations.of(context)!.lbl_function,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              softWrap: false,
              style: ThemeProvider.instance.textStyleMed14
                  .copyWith(color: currentIndex == 1 ? ThemeProvider.instance.primaryColor : darkGrey))
        ],
      ),
      Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 34.h,
            child: Padding(
              padding: EdgeInsets.only(bottom: 4.h),
              child: SvgPicture.asset(
                Assets.icons.icBarNotification,
                width: 20.w,
                height: 20.w,
                colorFilter: ColorFilter.mode(
                  2 == currentIndex ? ThemeProvider.instance.primaryColor : darkGrey,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          Text(AppLocalizations.of(context)!.lbl_notification,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              softWrap: false,
              style: ThemeProvider.instance.textStyleMed14
                  .copyWith(color: currentIndex == 2 ? ThemeProvider.instance.primaryColor : darkGrey))
        ],
      ),
      Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 34.h,
            child: Padding(
              padding: EdgeInsets.only(bottom: 4.h),
              child: SvgPicture.asset(
                Assets.icons.icBarSetting,
                width: 20.w,
                height: 20.w,
                colorFilter: ColorFilter.mode(
                  3 == currentIndex ? ThemeProvider.instance.primaryColor : darkGrey,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          Text(AppLocalizations.of(context)!.lbl_setting,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              softWrap: false,
              style: ThemeProvider.instance.textStyleMed14
                  .copyWith(color: currentIndex == 3 ? ThemeProvider.instance.primaryColor : darkGrey))
        ],
      ),
    ];
  }

  // _buildItem(
  //     {Key? key, required int index, required String icon, String? count}) {
  //   return badge.Badge(
  //     key: key,
  //     position: badge.BadgePosition.topEnd(top: -10.h, end: -14.w),
  //     showBadge: count != null,
  //     ignorePointer: true,
  //     onTap: () {},
  //     badgeContent: count != null
  //         ? Text(
  //             count.toString(),
  //             style: ThemeProvider.instance.textStyleMed10
  //                 .copyWith(color: Colors.white, fontWeight: FontWeight.normal),
  //           )
  //         : null,
  //     badgeAnimation: const badge.BadgeAnimation.rotation(
  //       animationDuration: Duration(seconds: 1),
  //       colorChangeAnimationDuration: Duration(seconds: 1),
  //       loopAnimation: false,
  //       curve: Curves.fastOutSlowIn,
  //       colorChangeAnimationCurve: Curves.easeInCubic,
  //     ),
  //     child: Padding(
  //       padding: EdgeInsets.only(left: 12.w, bottom: 4.h),
  //       child: SvgPicture.asset(
  //         icon,
  //         width: 22.w,
  //         height: 22.w,
  //         color: index == currentIndex
  //             ? ThemeProvider.instance.primaryColor
  //             : darkGrey,
  //       ),
  //     ),
  //   );
  // }

  void createTutorial() async {
    bool isViewToturial = await appSharedPreferences.getViewTutorial().timeout(
              const Duration(seconds: 2),
              onTimeout: () => false,
            ) ??
        false;
    debugPrint('isVIew = $isViewToturial, ismouted = $mounted ------------');
    if (!isViewToturial && mounted) {
      tutorialCoachMark = TutorialCoachMark(
        targets: _createTargets(),
        colorShadow: darkGrey,
        textSkip: AppLocalizations.of(context)!.skip,
        paddingFocus: 10,
        opacityShadow: 0.8,
        focusAnimationDuration: const Duration(milliseconds: kAnimationTutorialDuration),
        unFocusAnimationDuration: const Duration(milliseconds: kAnimationTutorialDuration),
        pulseAnimationDuration: const Duration(milliseconds: kAnimationTutorialDuration),
        onFinish: () {
          appSharedPreferences.setViewTutorial(true);
        },
        onClickTarget: (target) {
          debugPrint('onClickTarget: $target');
        },
        onClickTargetWithTapPosition: (target, tapDetails) {
          debugPrint("target: $target");
          debugPrint("clicked at position local: ${tapDetails.localPosition} - global: ${tapDetails.globalPosition}");
        },
        onClickOverlay: (target) {
          debugPrint('onClickOverlay: $target');
        },
        onSkip: () {
          appSharedPreferences.setViewTutorial(true);
          return true;
        },
      )..show(context: context);
    }
  }

  List<TargetFocus> _createTargets() {
    List<TargetFocus> targets = [];
    targets.add(
      TargetFocus(
        identify: "keyBottomNavigation1",
        keyTarget: keyBottomNavigation1,
        alignSkip: Alignment.topRight,
        contents: [
          TargetContent(
            align: ContentAlign.top,
            builder: (context, controller) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    AppLocalizations.of(context)!.lbl_tutorial_title1,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: 20.sp,
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 10.h),
                    child: Text(
                      AppLocalizations.of(context)!.lbl_tutorial_desc1,
                      style: TextStyle(color: white),
                    ),
                  ),
                  // ElevatedButton(
                  //   onPressed: () {
                  //     // controller.previous();
                  //   },
                  //   child: const Icon(Icons.chevron_left),
                  // ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        Assets.icons.icTutorialDown1,
                        height: 100.h,
                      ),
                    ],
                  )
                ],
              );
            },
          ),
        ],
      ),
    );

    targets.add(
      TargetFocus(
        identify: "keyBottomNavigation2",
        keyTarget: keyBottomNavigation2,
        alignSkip: Alignment.topRight,
        contents: [
          TargetContent(
            align: ContentAlign.top,
            builder: (context, controller) {
              return Row(
                children: [
                  SvgPicture.asset(
                    Assets.icons.icTutorialDown2,
                    height: 70.h,
                  ),
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.only(left: 10.h),
                      child: Text(
                        AppLocalizations.of(context)!.lbl_tutorial_desc2,
                        style: TextStyle(
                          color: white,
                        ),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );

    targets.add(
      TargetFocus(
        identify: "keyBottomNavigation3",
        keyTarget: keyBottomNavigation3,
        alignSkip: Alignment.topRight,
        contents: [
          TargetContent(
            align: ContentAlign.top,
            builder: (context, controller) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  Padding(
                    padding: EdgeInsets.only(bottom: 10.h),
                    child: Text(
                      AppLocalizations.of(context)!.lbl_tutorial_desc3,
                      style: TextStyle(
                        color: white,
                      ),
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        Assets.icons.icTutorialDown3,
                        height: 50.h,
                      ),
                    ],
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );

    targets.add(
      TargetFocus(
        identify: "keyBottomNavigation4",
        keyTarget: keyBottomNavigation4,
        alignSkip: Alignment.topRight,
        contents: [
          TargetContent(
            align: ContentAlign.top,
            builder: (context, controller) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  Padding(
                    padding: EdgeInsets.only(bottom: 10.h),
                    child: Text(
                      AppLocalizations.of(context)!.lbl_tutorial_desc4,
                      style: TextStyle(
                        color: white,
                      ),
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: 150.w),
                        child: SvgPicture.asset(
                          Assets.icons.icTutorialDown4,
                          height: 50.h,
                        ),
                      ),
                    ],
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );

    targets.add(
      TargetFocus(
        identify: "keyBottomNavigation5",
        keyTarget: keyBottomNavigation5,
        alignSkip: Alignment.topRight,
        contents: [
          TargetContent(
            align: ContentAlign.top,
            builder: (context, controller) {
              return Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.end,
                children: <Widget>[
                  Padding(
                    padding: EdgeInsets.only(right: 10.h),
                    child: Text(
                      AppLocalizations.of(context)!.lbl_tutorial_desc5,
                      style: TextStyle(
                        color: white,
                      ),
                    ),
                  ),
                  SvgPicture.asset(
                    Assets.icons.icTutorialDown5,
                    height: 50.h,
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );

    targets.add(
      TargetFocus(
        identify: "Change student",
        keyTarget: keyButton,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            builder: (context, controller) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  SvgPicture.asset(
                    Assets.icons.icTutorialChangeStudent,
                    height: 50.h,
                  ),
                  Padding(
                    padding: EdgeInsets.only(bottom: 10.h),
                    child: Text(
                      AppLocalizations.of(context)!.lbl_tutorial_desc6,
                      style: TextStyle(
                        color: white,
                      ),
                    ),
                  )
                ],
              );
            },
          ),
        ],
      ),
    );

    targets.add(
      TargetFocus(
        identify: "Intro group",
        keyTarget: keyButton1,
        contents: [
          TargetContent(
            align: ContentAlign.bottom,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  AppLocalizations.of(context)!.lbl_tutorial_title7,
                  style: ThemeProvider.instance.textStyleBold20.copyWith(color: white),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 10.h),
                  child: Text(
                    AppLocalizations.of(context)!.lbl_tutorial_desc7,
                    style: TextStyle(color: white),
                  ),
                )
              ],
            ),
          ),
        ],
        shape: ShapeLightFocus.RRect,
      ),
    );

    return targets;
  }
}

class AnimationNoScaling extends FloatingActionButtonAnimator {
  double? _x;
  double? _y;
  @override
  Offset getOffset({Offset? begin, Offset? end, double? progress}) {
    _x = begin!.dx + (end!.dx - begin.dx) * progress!;
    _y = begin.dy + (end.dy - begin.dy) * progress;
    return Offset(_x!, _y!);
  }

  @override
  Animation<double> getRotationAnimation({Animation<double>? parent}) {
    return Tween<double>(begin: 1.0, end: 1.0).animate(parent!);
  }

  @override
  Animation<double> getScaleAnimation({Animation<double>? parent}) {
    return Tween<double>(begin: 1.0, end: 1.0).animate(parent!);
  }
}
