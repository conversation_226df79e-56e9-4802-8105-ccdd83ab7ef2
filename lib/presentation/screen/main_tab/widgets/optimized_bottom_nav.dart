import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:com_uikit/uikit.dart';
import 'package:myapp/gen/assets.gen.dart';
import 'package:myapp/l10n/gen/app_localizations.dart';
// import 'package:myapp/ui/colors_util.dart';

class OptimizedBottomNavItem extends StatelessWidget {
  final String iconPath;
  final String label;
  final bool isActive;
  final VoidCallback onTap;

  const OptimizedBottomNavItem({
    Key? key,
    required this.iconPath,
    required this.label,
    required this.isActive,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final color = isActive ? ThemeProvider.instance.primaryColor : darkGrey;

    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            height: 34.h,
            child: Padding(
              padding: EdgeInsets.only(bottom: 4.h),
              child: SvgPicture.asset(
                iconPath,
                width: 20.w,
                height: 20.w,
                colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
              ),
            ),
          ),
          Text(
            label,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            softWrap: false,
            style: ThemeProvider.instance.textStyleMed14.copyWith(color: color),
          ),
        ],
      ),
    );
  }
}

class OptimizedBottomNavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const OptimizedBottomNavBar({
    Key? key,
    required this.currentIndex,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final items = [
      _NavItem(Assets.icons.icBarHome, AppLocalizations.of(context)!.lbl_home),
      _NavItem(Assets.icons.icFunction, AppLocalizations.of(context)!.lbl_function),
      _NavItem(Assets.icons.icBarChat, AppLocalizations.of(context)!.lbl_chat),
      _NavItem(Assets.icons.icBarSetting, AppLocalizations.of(context)!.lbl_setting),
    ];

    return Container(
      height: 80.h,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            offset: const Offset(0, 1),
            blurRadius: 12,
            spreadRadius: 0.5,
            color: lightGrey,
          ),
        ],
      ),
      child: Row(
        children: List.generate(items.length, (index) {
          if (index == 2) {
            // Add spacer for center FAB
            return Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: OptimizedBottomNavItem(
                      iconPath: items[index].iconPath,
                      label: items[index].label,
                      isActive: currentIndex == index,
                      onTap: () => onTap(index),
                    ),
                  ),
                  SizedBox(width: 60.w), // Space for FAB
                ],
              ),
            );
          }

          return Expanded(
            child: OptimizedBottomNavItem(
              iconPath: items[index].iconPath,
              label: items[index].label,
              isActive: currentIndex == index,
              onTap: () => onTap(index),
            ),
          );
        }),
      ),
    );
  }
}

class _NavItem {
  final String iconPath;
  final String label;

  const _NavItem(this.iconPath, this.label);
}
