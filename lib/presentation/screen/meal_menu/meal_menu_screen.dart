import 'package:com_common/common.dart';
import 'package:com_common/presentation/locale/locale_cubit.dart';
import 'package:com_uikit/uikit.dart';
import 'package:flutter/material.dart';
import 'package:myapp/constants/constant.dart';
import 'package:myapp/data/model/meal_menu_response/meal_menu_response.dart';
import 'package:myapp/gen/assets.gen.dart';
import 'package:myapp/l10n/gen/app_localizations.dart';
import 'package:myapp/presentation/cubit/change_student/change_student_cubit.dart';
import 'package:myapp/presentation/cubit/config/common_state.dart';
import 'package:myapp/presentation/cubit/meal_menu/meal_menu_cubit.dart';
import 'package:myapp/presentation/cubit/signin/signin_cubit.dart';
import 'package:myapp/presentation/screen/base_student/empty_list_widget.dart';
import 'package:myapp/presentation/screen/base_student/hotline_ultils.dart';
import 'package:myapp/presentation/screen/meal_menu/meal_item_widget.dart';
import 'package:myapp/di/dependency_injection.dart' as di;

class MealMenuScreen extends StatefulWidget {
  static const String routeName = '/meal_menu_screen';
  const MealMenuScreen({super.key});

  @override
  State<MealMenuScreen> createState() => _MealMenuScreenState();
}

class _MealMenuScreenState extends State<MealMenuScreen> with TickerProviderStateMixin {
  late AnimationController animationController;
  late ScrollController _scrollController;
  DateTime _selectedDay = DateTime.now();

  List<MealMenuResponse> _list = [];

  @override
  void initState() {
    animationController =
        AnimationController(duration: const Duration(milliseconds: kAnimationAppearDuration), vsync: this);
    _scrollController = ScrollController();

    super.initState();
  }

  @override
  void dispose() {
    animationController.dispose();
    _scrollController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
        providers: [
          BlocProvider<MealMenuCubit>(
              create: (_) => di.sl<MealMenuCubit>()
                ..get(
                    schoolId: (BlocProvider.of<ChangeStudentCubit>(context).currentStudent?.truongId ??
                            BlocProvider.of<SignInCubit>(context).storedResponse?.students?.firstOrNull?.truongId) ??
                        0,
                    dateTime: int.parse(_selectedDay.microsecondsSinceEpoch.toString().substring(0, 10)))),

          // BlocProvider<ScheduleCubit>(create: (_) => di.sl<ScheduleCubit>())
        ],
        child: Scaffold(
            backgroundColor: white,
            resizeToAvoidBottomInset: false,
            appBar: WidgetAppBar(
                title: AppLocalizations.of(context)!.lbl_meal_menu,
                elevation: 1,
                icon: Assets.icons.icCross,
                onClickIconLeft: () {
                  Navigator.of(context).pop();
                }),
            body: SafeArea(
              bottom: false,
              child: Stack(
                fit: StackFit.expand,
                children: [
                  Column(
                    children: [
                      _buildCalendar(context),
                      _buildBody(context),
                    ],
                  ),
                  // Positioned(
                  //     bottom: 20.h,
                  //     left: 20.h,
                  //     child: GestureDetector(
                  //         onTap: () => showSupportBottomSheet(
                  //               context,
                  //               type: [
                  //                 kHotlineMealsAndShuttleBus.toUpperCase()
                  //               ],
                  //             ),
                  //         child: Lottie.asset(Assets.lotties.lottieCs,
                  //             repeat: true,
                  //             reverse: true,
                  //             animate: true,
                  //             height: 80.w,
                  //             width: 80.w))),
                ],
              ),
            )));
  }

  _buildCalendar(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(15.w),
      child: EasyDateTimeLine(
        initialDate: _selectedDay,
        onDateChange: (buildContext, selectedDate) {
          setState(() {
            _selectedDay = selectedDate;
            loadData(buildContext);
          });
        },
        locale: BlocProvider.of<LocaleCubit>(context).appLocale?.languageCode ?? 'en',
        headerProps: const EasyHeaderProps(
          centerHeader: true,
          monthPickerType: MonthPickerType.switcher,
          dateFormatter: DateFormatter.fullDateMonthAsStrDY(),
        ),
        timeLineProps: const EasyTimeLineProps(),
        dayProps: EasyDayProps(
          dayStructure: DayStructure.dayStrDayNum,
          activeDayStyle: DayStyle(
            decoration: BoxDecoration(
              color: white,
              borderRadius: const BorderRadius.all(Radius.circular(8)),
              boxShadow: [
                BoxShadow(
                  color: ThemeProvider.instance.primaryColor,
                  blurRadius: 2,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  _buildBody(BuildContext context) {
    return BlocConsumer<MealMenuCubit, CommonState>(
      listener: (context, state) {
        state.maybeWhen(
          error: (error) {
            dialogUtils(context, error).then((_) => handleDefaultAction(context, error));
          },
          orElse: () {},
        );
      },
      builder: (context, state) {
        state.maybeWhen(
          orElse: () {},
          initial: () => const CircularProgressIndicator(),
          success: (list) {
            _list = list;
          },
        );
        final isWeekend = _selectedDay.weekday == DateTime.sunday || _selectedDay.weekday == DateTime.saturday;

        if (_list.isEmpty) {
          return Expanded(
            child: ListView(
              physics: const NeverScrollableScrollPhysics(),
              children: [
                Center(
                  child: EmptyListItemWidget(
                    message: isWeekend
                        ? AppLocalizations.of(context)!.lbl_weekend_bless
                        : AppLocalizations.of(context)!.lbl_empty_data,
                    style: ThemeProvider.instance.textStyleMed14.copyWith(
                        color: isWeekend ? ThemeProvider.instance.primaryColor : Colors.black,
                        fontSize: isWeekend ? 18 : 14),
                  ),
                ),
              ],
            ),
          );
        }
        return Expanded(
          child: Padding(
            padding: EdgeInsets.only(
              left: 16,
              right: 16,
            ),
            child: ListView.separated(
                controller: _scrollController
                  ..addListener(() {
                    // loadData(context);
                  }),
                separatorBuilder: (context, index) => SizedBox(
                      height: 0.h,
                    ),
                padding: EdgeInsets.only(bottom: 80),
                itemCount: _list.length,
                // physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemBuilder: ((context, index) {
                  final int count = _list.length;
                  final Animation<double> animation = Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(
                      parent: animationController,
                      curve: Interval((1 / count) * index, 1.0, curve: Curves.fastOutSlowIn)));
                  animationController.forward();
                  return AnimatedBuilder(
                      animation: animationController,
                      builder: (BuildContext context, Widget? child) {
                        return FadeTransition(
                            opacity: animation,
                            child: Transform(
                                transform: Matrix4.translationValues(0.0, 50 * (1.0 - animation.value), 0.0),
                                child: MealItemWidget(
                                  index: index,
                                  menu: _list[index],
                                  indentTop: index == 0 ? 40.h : 0,
                                  indentBottom: index == _list.length - 1 ? 20.h : 0,
                                )));
                      });
                })),
          ),
        );
      },
    );
  }

  Future<void> loadData(BuildContext context, {bool isFirstLoad = false}) async {
    animationController.reset();
    BlocProvider.of<MealMenuCubit>(context).get(
        schoolId: (BlocProvider.of<ChangeStudentCubit>(context).currentStudent?.truongId ??
                BlocProvider.of<SignInCubit>(context).storedResponse?.students?.firstOrNull?.truongId) ??
            0,
        dateTime: _selectedDay.millisecondsSinceEpoch ~/ 1000);
  }
}
