import 'package:com_uikit/uikit.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class MealGridPhotoGallery extends StatelessWidget {
  final List<String> images;
  final Function(int index) onTap;
  const MealGridPhotoGallery(
      {super.key, required this.images, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        // height: 200.w,
        child: GridView.custom(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverQuiltedGridDelegate(
          crossAxisCount: 4,
          mainAxisSpacing: 4,
          crossAxisSpacing: 4,
          repeatPattern: QuiltedGridRepeatPattern.inverted,
          pattern: getPattern()),
      childrenDelegate: SliverChildBuilderDelegate(
          (context, index) => GestureDetector(
                onTap: () => onTap(index),
                child: AbsorbPointer(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(5.r),
                    child: CachedNetworkImage(
                      imageUrl: images[index],
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ),
          childCount: images.length),
    ));
  }

  List<QuiltedGridTile> getPattern() {
    switch (images.length) {
      case 1:
        return [
          const QuiltedGridTile(4, 4),
        ];

      case 2:
        return [
          const QuiltedGridTile(2, 2),
          const QuiltedGridTile(2, 2),
        ];

      case 3:
        return [
          const QuiltedGridTile(4, 4),
          const QuiltedGridTile(2, 2),
          const QuiltedGridTile(2, 2),
        ];

      case 4:
        return [
          const QuiltedGridTile(4, 4),
          const QuiltedGridTile(2, 2),
          const QuiltedGridTile(1, 2),
          const QuiltedGridTile(1, 2),
        ];

      default:
        if (images.length < 8) {
          return [QuiltedGridTile(2, 2), QuiltedGridTile(2, 2)];
        }
        return [QuiltedGridTile(1, 1), QuiltedGridTile(1, 1)];
    }
  }
}
