import 'package:com_common/common.dart';
import 'package:com_uikit/uikit.dart';
import 'package:flutter/material.dart';
import 'package:myapp/constants/constant.dart';
import 'package:myapp/data/model/bus_history_response/bus_history_response.dart';
import 'package:myapp/data/model/bus_service_response/bus_service_response.dart';
import 'package:myapp/data/model/parent_response/person_model.dart';
import 'package:myapp/gen/assets.gen.dart';
import 'package:myapp/l10n/gen/app_localizations.dart';
import 'package:myapp/presentation/cubit/bus/cancel_bus_absence_cubit.dart';
import 'package:myapp/presentation/cubit/bus/cancel_bus_service_cubit.dart';
import 'package:myapp/presentation/cubit/bus/get_bus_absence_list_cubit.dart';
import 'package:myapp/presentation/cubit/bus/get_bus_current_service_cubit.dart';
import 'package:myapp/presentation/cubit/bus/get_bus_history_cubit.dart';
import 'package:myapp/presentation/cubit/change_student/change_student_cubit.dart';
import 'package:myapp/presentation/cubit/config/common_state.dart';

import 'package:myapp/presentation/cubit/signin/signin_cubit.dart';

import 'package:myapp/di/dependency_injection.dart' as di;
import 'package:myapp/presentation/screen/base_student/hotline_ultils.dart';
import 'package:myapp/presentation/screen/bus/bus_absence_list_widget.dart';
import 'package:myapp/presentation/screen/bus/bus_history_list_widgdt.dart';
import 'package:myapp/presentation/screen/bus/bus_information_widget.dart';
import 'package:myapp/presentation/screen/request_absent/absense_request_list_screen.dart';
import 'package:myapp/presentation/screen/request_absent/absent_model.dart';
import 'package:myapp/presentation/screen/request_absent/create_absent_request_screen.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

class BusScreen extends StatefulWidget {
  static const String routeName = '/bus_screen';
  final int? tabIndex;
  final DateTime? date;
  const BusScreen({super.key, this.date, this.tabIndex});

  @override
  State<BusScreen> createState() => _BusScreenState();
}

class _BusScreenState extends State<BusScreen> {
  DateTime _selectedDay = DateTime.now();
  final List<BusHistoryResponse> _list = [];

  late List<String> categories = [
    AppLocalizations.of(context)!.lbl_bus_history,
    // AppLocalizations.of(context)!.lbl_bus_information,
    AppLocalizations.of(context)!.lbl_daily_event_absent,
  ];
  late int selectedCategory = widget.tabIndex ?? (widget.date != null ? 0 : 1);
  PersonModel? hotlineModel;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
        providers: [
          BlocProvider<GetBusCurrentServiceCubit>(
              create: (_) => di.sl<GetBusCurrentServiceCubit>()
                ..get(
                    studentId: (BlocProvider.of<ChangeStudentCubit>(context).currentStudent?.id ??
                            BlocProvider.of<SignInCubit>(context).storedResponse?.students?.firstOrNull?.id) ??
                        0,
                    year: (BlocProvider.of<ChangeStudentCubit>(context).currentStudent?.namHoc ??
                            BlocProvider.of<SignInCubit>(context).storedResponse?.students?.firstOrNull?.namHoc) ??
                        '2024-2025')),
          BlocProvider<GetBusAbsenceListCubit>(
              create: (_) => di.sl<GetBusAbsenceListCubit>()
                ..get(
                    studentId: (BlocProvider.of<ChangeStudentCubit>(context).currentStudent?.id ??
                            BlocProvider.of<SignInCubit>(context).storedResponse?.students?.firstOrNull?.id) ??
                        0,
                    year: (BlocProvider.of<ChangeStudentCubit>(context).currentStudent?.namHoc ??
                            BlocProvider.of<SignInCubit>(context).storedResponse?.students?.firstOrNull?.namHoc) ??
                        '2024-2025')),
          BlocProvider<CancelBusServiceCubit>(create: (_) => di.sl<CancelBusServiceCubit>()),
          BlocProvider<CancelBusAbsenceCubit>(create: (_) => di.sl<CancelBusAbsenceCubit>()),
          BlocProvider<GetBusHistoryCubit>(
              create: (_) => di.sl<GetBusHistoryCubit>()
                ..get(
                    studentId: (BlocProvider.of<ChangeStudentCubit>(context).currentStudent?.id ??
                            BlocProvider.of<SignInCubit>(context).storedResponse?.students?.firstOrNull?.id) ??
                        0,
                    fromTime: int.parse((_selectedDay).microsecondsSinceEpoch.toString().substring(0, 10)),
                    toTime: int.parse((_selectedDay).microsecondsSinceEpoch.toString().substring(0, 10)))),

          // BlocProvider<ScheduleCubit>(create: (_) => di.sl<ScheduleCubit>())
        ],
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          backgroundColor: Colors.white,
          appBar: WidgetAppBar(
              title: AppLocalizations.of(context)!.lbl_bus_title,
              elevation: 1,
              hideBack: false,
              onClickIconLeft: () {
                Navigator.of(context).pop();
              }),
          body: SafeArea(
            child: Stack(
              fit: StackFit.expand,
              children: [
                Column(
                  // mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildCategory(context),
                    selectedCategory == 0
                        ? BusHistoryListWidget(
                            selectedDay: _selectedDay,
                            onDateChange: (date) {
                              setState(() {
                                _selectedDay = date;
                              });
                            },
                          )
                        : BusAbsenceListWidget()
                    //  (selectedCategory == 2
                    //     ? const BusAbsenceListWidget()
                    //     : const BusInformationWidget()),
                  ],
                ),
                // Positioned(
                //     bottom: 20.h,
                //     left: 20.h,
                //     child: BlocBuilder<GetBusCurrentServiceCubit, CommonState>(builder: (context, state) {
                //       state.maybeWhen(success: (data) {
                //         final list = data as List<BusServiceResponse>?;
                //         list?.firstOrNull?.let((it) {
                //           hotlineModel = PersonModel(
                //             fullName: AppLocalizations.of(context)!.lbl_bus_monitor,
                //             phoneNumber: it.busMonitoringDienthoai,
                //           );
                //         });
                //       }, orElse: () {
                //         return const SizedBox();
                //       });
                //       return GestureDetector(
                //           onTap: () => showSupportBottomSheet(
                //                 context,
                //                 type: [kHotlineMealsAndShuttleBus.toUpperCase()],
                //                 additionalHotline: hotlineModel,
                //               ),
                //           child: Lottie.asset(Assets.lotties.lottieCs,
                //               repeat: true, reverse: true, animate: true, height: 80.w, width: 80.w));
                //     })),
                Positioned(
                    bottom: 35.h,
                    right: 20.h,
                    child: Container(
                        height: 50.h,
                        padding: EdgeInsets.symmetric(horizontal: 8),
                        decoration: BoxDecoration(
                            color: ThemeProvider.instance.primaryColor, borderRadius: BorderRadius.circular(25.r)),
                        child: TextButton(
                          child: Row(
                            children: [
                              Icon(
                                Icons.bus_alert_outlined,
                                color: white,
                              ),
                              SizedBox(
                                width: 4,
                              ),
                              Text(
                                AppLocalizations.of(context)!.lbl_daily_event_absent,
                                style: ThemeProvider.instance.textStyleMed14.copyWith(color: Colors.white),
                              )
                            ],
                          ),
                          onPressed: () {
                            Navigator.of(context).pushNamed(CreateAbsentRequestScreen.routeName,
                                arguments: {'detail': null, 'bus': null, 'type': AbsentType.bus}).then((value) {
                              if (value == null) return;
                              if (value == false && selectedCategory != 2) {
                                setState(() {
                                  selectedCategory = 2;
                                  loadData(context, isFirstLoad: true);
                                });
                              } else if (value == true) {
                                Navigator.of(context).popAndPushNamed(
                                  AbsenseRequestListScreen.routeName,
                                );
                              }
                            });
                          },
                        )))
              ],
            ),
          ),
        ));
  }

  _buildCategory(BuildContext context) {
    return SizedBox(
      height: 60.h,
      child: ListView.separated(
          separatorBuilder: (context, index) => SizedBox(
                width: 10.w,
              ),
          scrollDirection: Axis.horizontal,
          padding: EdgeInsets.symmetric(vertical: 15.h, horizontal: 20.w),
          itemCount: categories.length,
          shrinkWrap: true,
          // physics: NeverScrollableScrollPhysics(),
          itemBuilder: ((context, index) {
            return GestureDetector(
              onTap: () {
                setState(() {
                  selectedCategory = index;
                });
              },
              child: AbsorbPointer(
                child: Container(
                    height: 50.h,
                    padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
                    alignment: AlignmentDirectional.center,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16.r),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.5),
                            blurRadius: 5, // changes position of shadow
                          ),
                        ],
                        // border: Border.all(
                        //     color: index == selectedCategory
                        //         ? ThemeProvider.instance.primaryColor
                        //         : darkGrey),
                        color: index == selectedCategory ? ThemeProvider.instance.secondaryColor : white),
                    child: Text(
                      categories[index],
                      style: ThemeProvider.instance.textStyleMed14
                          .copyWith(color: index == selectedCategory ? white : darkGrey),
                    )),
              ),
            );
          })),
    );
  }

  Future<void> loadData(BuildContext context, {bool isFirstLoad = false}) async {
    if (selectedCategory == 0) {
      BlocProvider.of<GetBusHistoryCubit>(context).get(
          studentId: (BlocProvider.of<ChangeStudentCubit>(context).currentStudent?.id ??
                  BlocProvider.of<SignInCubit>(context).storedResponse?.students?.firstOrNull?.id) ??
              0,
          fromTime: int.parse((_selectedDay).microsecondsSinceEpoch.toString().substring(0, 10)),
          toTime: int.parse((_selectedDay).microsecondsSinceEpoch.toString().substring(0, 10)));
    } else if (selectedCategory == 1) {
      BlocProvider.of<GetBusCurrentServiceCubit>(context).get(
          studentId: (BlocProvider.of<ChangeStudentCubit>(context).currentStudent?.id ??
                  BlocProvider.of<SignInCubit>(context).storedResponse?.students?.firstOrNull?.id) ??
              0,
          year: (BlocProvider.of<ChangeStudentCubit>(context).currentStudent?.namHoc ??
                  BlocProvider.of<SignInCubit>(context).storedResponse?.students?.firstOrNull?.namHoc) ??
              '2024-2025');
    } else {
      BlocProvider.of<GetBusAbsenceListCubit>(context).get(
          studentId: (BlocProvider.of<ChangeStudentCubit>(context).currentStudent?.id ??
                  BlocProvider.of<SignInCubit>(context).storedResponse?.students?.firstOrNull?.id) ??
              0,
          year: (BlocProvider.of<ChangeStudentCubit>(context).currentStudent?.namHoc ??
                  BlocProvider.of<SignInCubit>(context).storedResponse?.students?.firstOrNull?.namHoc) ??
              '2024-2025');
    }
  }
}
