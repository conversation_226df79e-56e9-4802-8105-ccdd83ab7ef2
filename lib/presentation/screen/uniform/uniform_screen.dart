import 'package:com_common/common.dart';
import 'package:com_common/presentation/locale/locale_cubit.dart';
import 'package:com_uikit/uikit.dart';
import 'package:com_uikit/utils/constant.dart';
import 'package:flutter/material.dart';
import 'package:collection/collection.dart';
import 'package:myapp/constants/constant.dart';
import 'package:myapp/data/model/response/uniform_cart_response/uniform_cart_response.dart';
import 'package:myapp/data/model/response/uniform_category_response/uniform_category_response.dart';
import 'package:myapp/data/model/response/uniform_product_response/product_model.dart';
import 'package:myapp/gen/assets.gen.dart';
import 'package:myapp/l10n/gen/app_localizations.dart';
import 'package:myapp/presentation/cubit/change_student/change_student_cubit.dart';
import 'package:myapp/presentation/cubit/config/common_state.dart';
import 'package:myapp/presentation/cubit/signin/signin_cubit.dart';
import 'package:myapp/presentation/cubit/uniform/uniform_cart_cubit.dart';
import 'package:myapp/presentation/cubit/uniform/uniform_category_cubit.dart';
import 'package:myapp/presentation/cubit/uniform/uniform_product_list_cubit.dart';
import 'package:myapp/presentation/cubit/uniform/uniform_standart_set_combo_list_cubit.dart';
import 'package:myapp/presentation/screen/base_student/hotline_ultils.dart';
import 'package:myapp/presentation/screen/uniform/uniform_cart_detail_screen.dart';
import 'package:myapp/presentation/screen/uniform/uniform_category_widget.dart';
import 'package:myapp/presentation/screen/uniform/uniform_grid_widget.dart';
import 'package:badges/badges.dart' as badge;
import 'package:myapp/di/dependency_injection.dart' as di;
import 'package:myapp/presentation/screen/uniform/uniform_order_list_screen.dart';
import 'package:myapp/presentation/screen/uniform/uniform_set_combo_screen.dart';

class UniformScreen extends StatefulWidget {
  static const String routeName = '/uniform_screen';
  const UniformScreen({super.key});

  @override
  State<UniformScreen> createState() => _UniformScreenState();
}

class _UniformScreenState extends State<UniformScreen> {
  late int tabbarIndex = 0;
  final List<UniformCategoryResponse> categories = [];
  final List<UniformProductModel> products = [];
  List<UniformCartResponse>? carts;
  int standartSetComboId = 0;

  int selectedCategoryId = -1;

  @override
  void initState() {
    super.initState();

    BlocProvider.of<UniformCartCubit>(context).get(
        studentId: (BlocProvider.of<ChangeStudentCubit>(context).currentStudent ??
                    BlocProvider.of<SignInCubit>(context).storedResponse?.students?.firstOrNull)
                ?.id ??
            0);
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
        providers: [
          BlocProvider<UniformCategoryCubit>(
            create: (_) => di.sl<UniformCategoryCubit>()
              ..get(
                studentId: (BlocProvider.of<ChangeStudentCubit>(context).currentStudent ??
                            BlocProvider.of<SignInCubit>(context).storedResponse?.students?.firstOrNull)
                        ?.id ??
                    0,
              ),
          ),
          BlocProvider<UniformStandartSetComboListCubit>(
            create: (_) => di.sl<UniformStandartSetComboListCubit>()
              ..get(
                studentId: (BlocProvider.of<ChangeStudentCubit>(context).currentStudent ??
                            BlocProvider.of<SignInCubit>(context).storedResponse?.students?.firstOrNull)
                        ?.id ??
                    0,
              ),
          ),
          BlocProvider<UniformProductListCubit>(
              create: (_) => di.sl<UniformProductListCubit>()
                ..get(
                    studentId: (BlocProvider.of<ChangeStudentCubit>(context).currentStudent ??
                                BlocProvider.of<SignInCubit>(context).storedResponse?.students?.firstOrNull)
                            ?.id ??
                        0,
                    categoryId: 0)),
        ],
        child: Scaffold(
            backgroundColor: white,
            resizeToAvoidBottomInset: false,
            appBar: WidgetAppBar(
                title: AppLocalizations.of(context)!.lbl_menu_uniform,
                elevation: 1,
                hideBack: false,
                widgetRight: Row(
                  children: [
                    IconButton(
                        onPressed: () => Navigator.of(context).pushNamed(UniformOrderListScreen.routeName, arguments: {
                              'objectId': null
                            }).then((value) => {
                                  BlocProvider.of<UniformCartCubit>(context).get(
                                      studentId: (BlocProvider.of<ChangeStudentCubit>(context).currentStudent ??
                                                  BlocProvider.of<SignInCubit>(context)
                                                      .storedResponse
                                                      ?.students
                                                      ?.firstOrNull)
                                              ?.id ??
                                          0)
                                }),
                        icon: Icon(
                          Icons.history_outlined,
                          size: 32.h,
                        )),
                    BlocConsumer<UniformCartCubit, CommonState>(
                      listener: (context, state) {
                        state.maybeWhen(
                          error: (error) {
                            dialogUtils(context, error).then((_) => handleDefaultAction(context, error));
                          },
                          orElse: () {},
                          success: (data) {
                            carts = data.orders;
                          },
                        );
                      },
                      builder: (context, state) {
                        final count = carts?.firstWhereOrNull((element) => element.setId == 0)?.items?.length ?? 0;
                        return IconButton(
                            onPressed: () {
                              if (carts?.isNotEmpty == true) {
                                Navigator.of(context).pushNamed(UniformCartDetailScreen.routeName,
                                    arguments: {'detail': null, 'index': null}).then((value) {
                                  BlocProvider.of<UniformCartCubit>(context).get(
                                      studentId: (BlocProvider.of<ChangeStudentCubit>(context).currentStudent ??
                                                  BlocProvider.of<SignInCubit>(context)
                                                      .storedResponse
                                                      ?.students
                                                      ?.firstOrNull)
                                              ?.id ??
                                          0);
                                });
                              } else {
                                showToastWidget(
                                  Padding(
                                    padding: EdgeInsets.all(16.w),
                                    child: Container(
                                        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                                        decoration: BoxDecoration(
                                            color: white,
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.grey.withOpacity(0.5),
                                                blurRadius: 5, // changes position of shadow
                                              ),
                                            ],
                                            borderRadius: BorderRadius.circular(kLargeInputBorderRadius)),
                                        child: Text(AppLocalizations.of(context)!.lbl_empty_cart)),
                                  ),
                                  context: context,
                                  isIgnoring: false,
                                  duration: const Duration(seconds: 2),
                                  animDuration: const Duration(milliseconds: 300),
                                  animation: StyledToastAnimation.slideFromTopFade,
                                  position: StyledToastPosition.bottom,
                                );
                              }
                            },
                            icon: badge.Badge(
                              position: badge.BadgePosition.topEnd(top: 0.h, end: -30.w),
                              showBadge: count > 0,
                              ignorePointer: true,
                              onTap: () {},
                              badgeContent: count > 0
                                  ? Text(
                                      count.toString(),
                                      style: ThemeProvider.instance.textStyleMed10
                                          .copyWith(color: Colors.white, fontWeight: FontWeight.normal),
                                    )
                                  : null,
                              badgeAnimation: const badge.BadgeAnimation.rotation(
                                animationDuration: Duration(seconds: 1),
                                colorChangeAnimationDuration: Duration(seconds: 1),
                                loopAnimation: false,
                                curve: Curves.fastOutSlowIn,
                                colorChangeAnimationCurve: Curves.easeInCubic,
                              ),
                              child: Padding(
                                padding: EdgeInsets.only(bottom: 4.h),
                                child: Icon(
                                  Icons.shopping_bag_outlined,
                                  size: 32.h,
                                  color: carts?.isNotEmpty == true ? black : darkGrey,
                                ),
                              ),
                            ));
                      },
                    ),
                  ],
                ),
                onClickIconLeft: () {
                  Navigator.of(context).pop();
                }),
            body: SafeArea(
              bottom: false,
              child: _buildContent(context),
            )));
  }

  _buildCategory(BuildContext context) {
    return BlocConsumer<UniformCategoryCubit, CommonState>(
      listener: (context, state) {
        state.maybeWhen(
          error: (error) {
            dialogUtils(context, error).then((_) => handleDefaultAction(context, error));
          },
          orElse: () {},
          success: (data) {
            categories.clear();
            categories.add(const UniformCategoryResponse(id: 0, name: 'Tất cả', nameEn: 'All items'));
            categories.addAll(data);

            selectedCategoryId = categories.first.id ?? 0;
            // loadProductData(context);
          },
        );
      },
      builder: (context, state) {
        return UniformCategoryWidget(
          list: categories,
          callback: (index) {
            selectedCategoryId = categories[index].id ?? 0;
            loadProductData(context);
          },
        );
      },
    );
  }

  _buildContent(BuildContext context) {
    return BlocConsumer<UniformProductListCubit, CommonState>(
      listener: (context, state) {
        state.maybeWhen(
          error: (error) {
            dialogUtils(context, error).then((_) => handleDefaultAction(context, error));
          },
          orElse: () {},
          success: (data) {
            products.clear();
            products.addAll(data);
          },
        );
      },
      builder: (context, state) {
        return Stack(
          fit: StackFit.expand,
          children: [
            Column(
              children: [
                _buildCategory(context),
                Expanded(child: UniformGridWidget(list: products)),
              ],
            ),
            // Positioned(
            //     bottom: 20.h,
            //     left: 20.h,
            //     child: GestureDetector(
            //         onTap: () => showSupportBottomSheet(
            //               context,
            //               type: [kHotlineUniforms.toUpperCase()],
            //             ),
            //         child: Lottie.asset(Assets.lotties.lottieCs,
            //             repeat: true,
            //             reverse: true,
            //             animate: true,
            //             height: 80.w,
            //             width: 80.w))),
            BlocConsumer<UniformStandartSetComboListCubit, CommonState>(
              listener: (context, state) {
                state.maybeWhen(
                  error: (error) {
                    dialogUtils(context, error).then((_) => handleDefaultAction(context, error));
                  },
                  orElse: () {},
                  success: (data) {
                    final standartSetCombo = data as List<UniformProductModel>?;

                    if (standartSetCombo?.isEmpty == true) {
                      return;
                    }
                    standartSetComboId = standartSetCombo?.firstOrNull?.id ?? 0;

                    Navigator.of(context).pushNamed(
                      UniformSetComboScreen.routeName,
                      arguments: {
                        'id': standartSetCombo?.firstOrNull?.id,
                        'name':
                            standartSetCombo?.firstOrNull?.getName(BlocProvider.of<LocaleCubit>(context).isVietnamese),
                        'isStandardCombo': true
                      },
                    );
                  },
                );
              },
              builder: (context, state) {
                return const SizedBox();
              },
            )
          ],
        );
      },
    );
  }

  Future<void> loadProductData(BuildContext context, {bool isFirstLoad = false}) async {
    BlocProvider.of<UniformProductListCubit>(context).get(
        studentId: (BlocProvider.of<ChangeStudentCubit>(context).currentStudent ??
                    BlocProvider.of<SignInCubit>(context).storedResponse?.students?.firstOrNull)
                ?.id ??
            0,
        categoryId: selectedCategoryId);
    // } else {
    //   BlocProvider.of<UniformSetComboListCubit>(context).get(
    //       studentId:
    //           (BlocProvider.of<ChangeStudentCubit>(context).currentStudent ??
    //                       BlocProvider.of<SignInCubit>(context)
    //                           .storedResponse
    //                           ?.students
    //                           ?.firstOrNull)
    //                   ?.id ??
    //               0);
    // }
  }
}
