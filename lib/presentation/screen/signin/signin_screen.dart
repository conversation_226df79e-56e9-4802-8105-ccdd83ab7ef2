import 'dart:io';
import 'package:com_common/common.dart';
import 'package:com_common/core/common_shared_preferences.dart';
import 'package:com_uikit/uikit.dart';
import 'package:com_uikit/utils/constant.dart';
import 'package:com_uikit/widget/dialog/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:myapp/constants/constant.dart';
import 'package:myapp/data/model/sign_in_response/student.dart';
import 'package:myapp/gen/assets.gen.dart';
import 'package:myapp/l10n/gen/app_localizations.dart';
import 'package:myapp/presentation/cubit/change_student/change_student_cubit.dart';
import 'package:myapp/presentation/cubit/signin/signin_cubit.dart';
import 'package:myapp/presentation/cubit/signin/signin_state.dart';
import 'package:myapp/presentation/screen/main_tab/main_home_screen.dart';
import 'package:myapp/presentation/screen/signin/input_mail_form.dart';
import 'package:myapp/presentation/screen/signin/otp_form.dart';
import 'package:myapp/presentation/screen/signin/signin_footer.dart';
import 'package:myapp/presentation/screen/signin/signin_form.dart';
import 'package:myapp/di/dependency_injection.dart' as di;

class SignInScreen extends StatefulWidget {
  static const String routeName = '/signin';
  const SignInScreen({super.key});

  @override
  State<SignInScreen> createState() => _SignInScreenState();
}

class _SignInScreenState extends State<SignInScreen> with TickerProviderStateMixin {
  final appSharedPreferences = di.sl<AppCommonSharedPreferences>();
  final FocusNode _focus1 = FocusNode();
  final FocusNode _focus2 = FocusNode();
  late final PageController _pageController = PageController(initialPage: 0);
  late final cubit = BlocProvider.of<SignInCubit>(context);
  String phoneNumber = '';
  String email = '';
  bool isMustUpdateEmail = false;
  bool isTyping = false;
  final LoadingOverlay _loadingOverlay = LoadingOverlay();
  @override
  void initState() {
    super.initState();

    _focus1.addListener(_onFocusChange);
    _focus2.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    super.dispose();
    _focus1.removeListener(_onFocusChange);
    _focus1.dispose();
    _focus2.removeListener(_onFocusChange);
    _focus2.dispose();
    _pageController.dispose();
  }

  void _onFocusChange() {
    setState(() {
      isTyping = _focus1.hasFocus || _focus2.hasFocus;
    });
  }

  //**********
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
          backgroundColor: ThemeProvider.instance.secondaryColor,
          resizeToAvoidBottomInset: true,
          extendBody: true,
          appBar: AppBar(
            backgroundColor: ThemeProvider.instance.secondaryColor,
            systemOverlayStyle: Platform.isIOS
                ? SystemUiOverlayStyle.dark
                : SystemUiOverlayStyle(
                    statusBarColor: ThemeProvider.instance.secondaryColor,
                    statusBarBrightness: Brightness.dark,
                    statusBarIconBrightness: Brightness.light, // status bar icons' color
                    systemNavigationBarIconBrightness: Brightness.light),
            elevation: 0,
            toolbarHeight: 0, // Status bar color
          ),
          body: Column(
            children: [
              AnimatedContainer(
                margin: EdgeInsets.symmetric(vertical: 24.h, horizontal: isTyping ? 120.w : 40.w),
                duration: const Duration(milliseconds: kAnimationAppearDuration),
                color: ThemeProvider.instance.secondaryColor,
                height: isTyping ? 120.h : 253.h,
                width: 428.w,
                child: Image.asset(
                  Assets.images.icLogo.path,
                  fit: BoxFit.contain,
                  // color: white,
                  // height: isTyping ? 40.h : 80.h,
                ),
              ),
              // _buildLogo(isTyping),
              _buildSignInProgress(context)
            ],
          )),
    );
  }

  _buildSignInProgress(BuildContext context) {
    return BlocConsumer<SignInCubit, SignInState>(
      listener: ((context, state) {
        state.maybeWhen(
          loading: () {
            _loadingOverlay.show(context);
          },
          error: (error) {
            _loadingOverlay.hide();
            dialogUtils(context, error).then((_) => handleDefaultAction(context, error));
          },
          orElse: () {
            _loadingOverlay.hide();
          },
          resetPassword: (name) {
            _loadingOverlay.hide();
            // _requestOtp();
            // _gotoOTP(name);
          },
          emptyStudent: () {
            _loadingOverlay.hide();
            dialogUtils(context, CustomErrorException(AppLocalizations.of(context)!.lbl_error_empty_student));
          },
          successOTP: (isResendFlow) {
            _loadingOverlay.hide();
            if (isResendFlow) return;

            _pageController.nextPage(
                duration: const Duration(milliseconds: kAnimationTutorialDuration), curve: Curves.easeIn);

            _focus2.requestFocus();
          },
          successSignIn: (data) {
            _loadingOverlay.hide();

            if (mounted) {
              Student? student = data?.students?.firstOrNull;
              if (student != null) {
                BlocProvider.of<ChangeStudentCubit>(context)
                    .change(student: student, id: data?.id ?? 0, fullName: data?.fullName ?? '');
              }
            }

            Navigator.of(context).pushReplacementNamed(MainHomeScreen.routeName);
            // _pageController.nextPage(
            //     duration:
            //         const Duration(milliseconds: kAnimationTutorialDuration),
            //     curve: Curves.easeIn);
          },
          updateEmail: () {
            _loadingOverlay.hide();
            // debugPrint('MOVE TO UPDATE EMAIL');
            isMustUpdateEmail = true;
            _pageController.nextPage(
                duration: const Duration(milliseconds: kAnimationTutorialDuration), curve: Curves.easeIn);
            _focus1.requestFocus();
          },
          otpUpdateEmail: (isResendFlow) {
            _loadingOverlay.hide();
            // debugPrint('MOVE TO OTP FOR UPDATE EMAIL');
            if (isResendFlow) return;

            _pageController.nextPage(
                duration: const Duration(milliseconds: kAnimationTutorialDuration), curve: Curves.easeIn);
            _focus2.requestFocus();
          },
        );
      }),
      builder: (context, state) {
        return Expanded(
          child: Stack(
            fit: StackFit.expand,
            children: [
              // Positioned(
              //     top: 0,
              //     right: 40.w,
              //     child: Image.asset(
              //       Assets.images.icPanda.path,
              //       fit: BoxFit.cover,
              //       height: 200.h,
              //     )),
              Container(
                margin: EdgeInsets.only(top: 130.h),
                decoration: BoxDecoration(
                    color: white,
                    borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(kLargeInputBorderRadius),
                        topRight: Radius.circular(kLargeInputBorderRadius))),
                child: Column(
                  children: [
                    const SizedBox(
                      height: 8,
                    ),
                    Expanded(
                        child: PageView(
                      controller: _pageController,
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        SigninFormWidget(
                          key: const ValueKey<int>(0),
                          callback: (phone) {
                            // FocusManager.instance.primaryFocus?.unfocus();
                            // _focus2.requestFocus();
                            phoneNumber = phone;
                            final username = phoneNumber.isValidEmail
                                ? phoneNumber
                                : phoneNumber.normalizeVietnamePhoneNumber.trim();
                            cubit.getOTP(phoneNumber: username);
                          },
                          node: _focus1,
                        ),
                        OtpFormWidget(
                          key: const ValueKey<int>(1),
                          callback: (otp) async {
                            // FocusManager.instance.primaryFocus?.unfocus();
                            // _focus1.requestFocus();
                            String token = await appSharedPreferences.getFCMToken() ?? '';
                            final username = phoneNumber.isValidEmail
                                ? phoneNumber
                                : phoneNumber.normalizeVietnamePhoneNumber.trim();
                            cubit.signin(username, otp, token);
                          },
                          isUpdateMailFlow: false,
                          onCancel: () {
                            _pageController.previousPage(
                                duration: const Duration(milliseconds: kAnimationTutorialDuration),
                                curve: Curves.easeIn);
                          },
                          onResend: () {
                            final username = phoneNumber.isValidEmail
                                ? phoneNumber
                                : phoneNumber.normalizeVietnamePhoneNumber.trim();
                            cubit.getOTP(phoneNumber: username, isResendFlow: true);
                          },
                          node: _focus2,
                        ),
                        if (isMustUpdateEmail)
                          InputMailFormWidget(
                            key: const ValueKey<int>(2),
                            callback: (value) {
                              // email = value;
                              // cubit.updateEmail(email: email);
                            },
                            node: _focus1,
                          ),
                        if (isMustUpdateEmail)
                          OtpFormWidget(
                            key: const ValueKey<int>(3),
                            callback: (otp) async {
                              // FocusManager.instance.primaryFocus?.unfocus();
                              // _focus1.requestFocus();
                              // cubit.submitOtpForUpdateEmail(otp: otp, email: email);
                            },
                            isUpdateMailFlow: false,
                            onCancel: () {
                              _pageController.jumpToPage(0);
                            },
                            onResend: () {
                              // cubit.updateEmail(email: email, isResendFlow: true);
                            },
                            node: _focus2,
                          ),
                      ],
                    )),
                    const SigninFooter()
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
