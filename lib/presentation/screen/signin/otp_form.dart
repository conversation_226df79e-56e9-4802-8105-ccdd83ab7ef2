import 'dart:async';

import 'package:com_common/common.dart';
import 'package:com_common/core/common_shared_preferences.dart';
import 'package:com_uikit/uikit.dart';
import 'package:com_uikit/utils/constant.dart';
import 'package:flutter/material.dart';
import 'package:myapp/l10n/gen/app_localizations.dart';
import 'package:myapp/di/dependency_injection.dart' as di;
import 'package:pinput/pinput.dart';

class OtpFormWidget extends StatefulWidget {
  final Function(String) callback;
  final FocusNode? node;
  final bool isUpdateMailFlow;
  final VoidCallback onCancel;
  final VoidCallback onResend;
  final Timer? timer;
  const OtpFormWidget(
      {super.key,
      required this.callback,
      required this.onCancel,
      this.isUpdateMailFlow = false,
      required this.onResend,
      this.timer,
      this.node});

  @override
  State<OtpFormWidget> createState() => _OtpFormWidgetState();
}

class _OtpFormWidgetState extends State<OtpFormWidget> {
  final appSharedPreferences = di.sl<AppCommonSharedPreferences>();
  final TextEditingController _otpController = TextEditingController();
  late String currentVersion = '';
  bool _isValided = false;
  late final int _countdownSeconds = 120;
  Timer? _timer;
  late int _remainedSeconds = 0;
  bool isVerifyCapcha = false;

  @override
  void initState() {
    super.initState();

    appSharedPreferences.setOnboarded(true);
  }

  @override
  void dispose() {
    _otpController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  _startTimer() {
    if (mounted) {
      _remainedSeconds = _countdownSeconds;
      const onesec = Duration(seconds: 1);
      _timer = Timer.periodic(onesec, (timer) {
        if (_remainedSeconds <= 0) {
          _timer?.cancel();
        } else {
          setState(() {
            _remainedSeconds -= 1;
          });
          // _appOTPCubit.resetState();
        }
      });
    }
  }

  String _formatTimeRemained() {
    var minute = _remainedSeconds ~/ 60;
    var second = _remainedSeconds % 60;
    var f = NumberFormat('00', 'en_US');
    if (minute == 0) {
      return f.format(second);
    }
    return '${f.format(minute)}:${f.format(second)}';
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildForm(),
        const Expanded(child: SizedBox()),
        _buildButton(),
      ],
    );
  }

  _buildForm() {
    final defaultPinTheme = PinTheme(
      width: 56.w,
      height: 56.w,
      textStyle: ThemeProvider.instance.textStyleBold16,
      decoration: BoxDecoration(
        border: Border.all(color: lightGrey),
        borderRadius: BorderRadius.circular(8),
      ),
    );

    final focusedPinTheme = defaultPinTheme.copyDecorationWith(
      border: Border.all(color: ThemeProvider.instance.primaryColor),
      borderRadius: BorderRadius.circular(8),
    );

    final submittedPinTheme = defaultPinTheme.copyWith(
      decoration: defaultPinTheme.decoration?.copyWith(
        color: const Color.fromRGBO(234, 239, 243, 1),
      ),
    );
    return Padding(
      padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 20.w),
      child: Column(
        children: [
          Row(
            children: [
              Padding(
                padding: EdgeInsets.only(left: 8.w, bottom: 10.h),
                child: Text(
                  AppLocalizations.of(context)!.lbl_verify_otp,
                  style: ThemeProvider.instance.textStyleMed18,
                  textAlign: TextAlign.start,
                ),
              ),
            ],
          ),
          Pinput(
            controller: _otpController,
            focusNode: widget.node,
            keyboardAppearance: Brightness.light,
            keyboardType: TextInputType.number,
            defaultPinTheme: defaultPinTheme,
            focusedPinTheme: focusedPinTheme,
            submittedPinTheme: submittedPinTheme,
            length: 6,
            onCompleted: (value) {
              widget.callback(value);
            },
            onChanged: (value) => setState(() {
              _isValided = value.length == 6;
            }),
          ),
          SizedBox(height: 10.h),
          Row(
            children: [
              InkWell(
                child: Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 8.w, vertical: 12.h),
                  child: _remainedSeconds > 0
                      ? Text(
                          '${AppLocalizations.of(context)!.lbl_send_again} (${_formatTimeRemained()})',
                          style: ThemeProvider.instance.textStyleMed16
                              .copyWith(color: darkGrey))
                      : Text(AppLocalizations.of(context)!.lbl_send_again,
                          style: ThemeProvider.instance.textStyleMed16.copyWith(
                              color: ThemeProvider.instance.secondaryColor)),
                ),
                onTap: () {
                  if (_remainedSeconds <= 0) {
                    _startTimer();
                    _otpController.clear();
                    widget.onResend.call();
                  }
                },
              ),
            ],
          ),
          SizedBox(height: 15.h),
        ],
      ),
    );
  }

  _buildButton() {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 30.w,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            children: [
              Expanded(
                child: CustomButton(
                    isDisable: !_isValided,
                    style: CustomButtonStyle.plain,
                    colorBorder: ThemeProvider.instance.secondaryColor,
                    text:
                        AppLocalizations.of(context)!.lbl_verify.toUpperCase(),
                    colorBackground: ThemeProvider.instance.secondaryColor,
                    cornerRadius: kBigInputBorderRadius,
                    titleStyle: ThemeProvider.instance.textStyleMed18
                        .copyWith(color: white),
                    onPressed: () => widget.callback(_otpController.text)),
              ),
            ],
          ),
          InkWell(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 12.h),
              child: Text(AppLocalizations.of(context)!.lbl_cancel,
                  style: ThemeProvider.instance.textStyleMed16
                      .copyWith(color: ThemeProvider.instance.secondaryColor)),
            ),
            onTap: () => widget.onCancel(),
          ),
          SizedBox(
            height: 10.h,
          ),
        ],
      ),
    );
  }
}
