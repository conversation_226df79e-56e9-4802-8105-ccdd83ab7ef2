import 'package:com_common/common.dart';
import 'package:com_common/core/common_shared_preferences.dart';
import 'package:com_uikit/uikit.dart';
import 'package:com_uikit/utils/constant.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:myapp/l10n/gen/app_localizations.dart';
import 'package:myapp/di/dependency_injection.dart' as di;
import 'package:myapp/presentation/screen/guest_home/guest_main_screen.dart';
// import 'package:myapp/presentation/screen/guest_home/guest_home_screen.dart';

class SigninFormWidget extends StatefulWidget {
  final FocusNode? node;
  final Function(String) callback;
  const SigninFormWidget({super.key, required this.callback, this.node});

  @override
  State<SigninFormWidget> createState() => _SigninFormWidgetState();
}

class _SigninFormWidgetState extends State<SigninFormWidget> {
  final appSharedPreferences = di.sl<AppCommonSharedPreferences>();
  final TextEditingController _phoneController =
      TextEditingController(); //0978709999
  late String currentVersion = '';
  bool _isValided = false;
  bool isPhoneFormat = false;
  String? errorMsg;

  @override
  void initState() {
    // _phoneController.addListener(() {
    //   setState(() {
    //     debugPrint(
    //         'PHONE = ${_phoneController.text} == ${_isNumeric(_phoneController.text)} -> ${_phoneController.text.rawVietnamePhoneNumber} == ${_isNumeric(_phoneController.text.rawVietnamePhoneNumber)}');
    //     isPhoneFormat = _isNumeric(_phoneController.text) ||
    //         _isNumeric(_phoneController.text.rawVietnamePhoneNumber);
    //     debugPrint(
    //         'IS PHONE = ${_phoneController.text.isValidPhoneNumber}--- IS EMAIL = ${_phoneController.text.isValidEmail}');
    //     _isValided = _phoneController.text.isValidPhoneNumber ||
    //         _phoneController.text.isValidEmail;
    //   });
    // });

    super.initState();

    appSharedPreferences.setOnboarded(true);
  }

  @override
  void dispose() {
    super.dispose();
    _phoneController.dispose();
  }

  bool _isNumeric(String? str) {
    if (str == null || str.isEmpty == true) {
      return false;
    }
    return double.tryParse(str) != null;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildForm(),
        // const Expanded(child: SizedBox()),
        _buildButton(),
      ],
    );
  }

  _buildForm() {
    return Padding(
      padding: EdgeInsets.only(
        left: 20.w,
      ),
      child: Column(
        children: [
          CommonInputTextFormField(
            controller: _phoneController,
            focusNode: widget.node,
            autoFocus: true,
            keyboardType: TextInputType.phone,
            enableClear: true,
            inputFormatters: isPhoneFormat
                ? [
                    PhoneNumberTextFormatter(),
                    LengthLimitingTextInputFormatter(17)
                  ]
                : null,
            inputAction: TextInputAction.next,
            textStyle:
                ThemeProvider.instance.textStyleMed18.copyWith(fontSize: 24),
            inputDecoration: InputDecoration(
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
                labelStyle: TextStyle(color: darkGrey),
                focusedBorder: InputBorder.none,
                enabledBorder: InputBorder.none,
                border: InputBorder.none,
                labelText: AppLocalizations.of(context)!.lbl_uname,
                hintText: AppLocalizations.of(context)!.lbl_uname_placeholder,
                hintStyle: ThemeProvider.instance.textStyleMed18
                    .copyWith(color: disableGrey, fontSize: 24)),
            hint: AppLocalizations.of(context)!.lbl_phone,
            errorMsg: errorMsg,
            onChange: (newValue) {
              // debugPrint('CHange value -> ${p0.toString()}');
              // print('Phone field changed! Is now $txt');
              String username = newValue;
              if (_isNumeric(newValue) && !isPhoneFormat) {
                username = newValue.formatSurfixPhoneNumber.formatPhoneNumber;
              }

              setState(() {
                _phoneController.text = username;
                // debugPrint(
                //     'PHONE = $username == ${_isNumeric(username)} -> ${username.rawVietnamePhoneNumber} == ${_isNumeric(username.rawVietnamePhoneNumber)}');
                // isPhoneFormat = _isNumeric(username) ||
                //     _isNumeric(username.rawVietnamePhoneNumber);
                // debugPrint(
                //     'IS PHONE = ${username.isValidPhoneNumber}--- IS EMAIL = ${username.isValidEmail}');
                _isValided =
                    username.isValidPhoneNumber || username.isValidEmail;
                errorMsg = _isValided
                    ? null
                    : AppLocalizations.of(context)!.lbl_valid_username;
              });
            },
          ),
          SizedBox(height: 15.h),
        ],
      ),
    );
  }

  _buildButton() {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 30.w,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            children: [
              Expanded(
                child: CustomButton(
                    isDisable: !_isValided,
                    style: CustomButtonStyle.plain,
                    colorBorder: ThemeProvider.instance.primaryColor,
                    text:
                        AppLocalizations.of(context)!.lbl_signin.toUpperCase(),
                    colorBackground: ThemeProvider.instance.primaryColor,
                    cornerRadius: kBigInputBorderRadius,
                    titleStyle: ThemeProvider.instance.textStyleMed18
                        .copyWith(color: white),
                    onPressed: () => widget.callback(_phoneController.text)),
              ),
            ],
          ),
          InkWell(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 12.h),
              child: Text(AppLocalizations.of(context)!.lbl_im_guest,
                  style: ThemeProvider.instance.textStyleMed16
                      .copyWith(color: ThemeProvider.instance.secondaryColor)),
            ),
            onTap: () {
              Navigator.of(context).pushReplacementNamed(
                GuestMainScreen.routeName,
              );
            },
          ),
          SizedBox(
            height: 10.h,
          ),
        ],
      ),
    );
  }
}
