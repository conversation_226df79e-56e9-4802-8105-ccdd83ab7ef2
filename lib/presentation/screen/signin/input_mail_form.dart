import 'package:com_common/common.dart';
import 'package:com_common/core/common_shared_preferences.dart';
import 'package:com_uikit/uikit.dart';
import 'package:com_uikit/utils/constant.dart';
import 'package:flutter/material.dart';
import 'package:myapp/l10n/gen/app_localizations.dart';
import 'package:myapp/di/dependency_injection.dart' as di;

class InputMailFormWidget extends StatefulWidget {
  final Function(String) callback;
  final FocusNode? node;
  const InputMailFormWidget({super.key, required this.callback, this.node});

  @override
  State<InputMailFormWidget> createState() => _InputMailFormWidgetState();
}

class _InputMailFormWidgetState extends State<InputMailFormWidget> {
  final appSharedPreferences = di.sl<AppCommonSharedPreferences>();
  final TextEditingController _phoneController =
      TextEditingController(); //0906181211
  late String currentVersion = '';
  bool _isValided = false;

  @override
  void initState() {
    _phoneController.addListener(() {
      setState(() {
        _isValided = _phoneController.text.isValidEmail;
      });
    });

    super.initState();

    appSharedPreferences.setOnboarded(true);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildForm(),
        const Expanded(child: SizedBox()),
        _buildButton(),
      ],
    );
  }

  _buildForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 20.w, right: 20.w, bottom: 10.h),
          child: Text(
            AppLocalizations.of(context)!.lbl_email,
            style: ThemeProvider.instance.textStyleMed14
                .copyWith(color: Colors.red),
          ),
        ),
        CommonInputTextFormField(
          controller: _phoneController,
          focusNode: widget.node,
          keyboardType: TextInputType.emailAddress,
          inputAction: TextInputAction.next,
          textStyle:
              ThemeProvider.instance.textStyleMed18.copyWith(fontSize: 24),
          inputDecoration: InputDecoration(
              contentPadding:
                  EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
              labelStyle: TextStyle(color: darkGrey),
              focusedBorder: InputBorder.none,
              enabledBorder: InputBorder.none,
              border: InputBorder.none,
              labelText: AppLocalizations.of(context)!.lbl_email,
              hintText: '<EMAIL>',
              hintStyle: ThemeProvider.instance.textStyleMed18
                  .copyWith(color: disableGrey, fontSize: 24)),
          hint: AppLocalizations.of(context)!.lbl_email,
        ),
        SizedBox(height: 15.h),
      ],
    );
  }

  _buildButton() {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 30.w,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            children: [
              Expanded(
                child: CustomButton(
                    isDisable: !_isValided,
                    style: CustomButtonStyle.plain,
                    colorBorder: ThemeProvider.instance.secondaryColor,
                    text:
                        AppLocalizations.of(context)!.lbl_verify.toUpperCase(),
                    colorBackground: ThemeProvider.instance.secondaryColor,
                    cornerRadius: kBigInputBorderRadius,
                    titleStyle: ThemeProvider.instance.textStyleMed18
                        .copyWith(color: white),
                    onPressed: () => widget.callback(_phoneController.text)),
              ),
            ],
          ),
          // InkWell(
          //   child: Padding(
          //     padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 12.h),
          //     child: Text(AppLocalizations.of(context)!.lbl_forgot,
          //         style: ThemeProvider.instance.textStyleMed16
          //             .copyWith(color: ThemeProvider.instance.secondaryColor)),
          //   ),
          //   onTap: () {

          //   },
          // ),
          SizedBox(
            height: 10.h,
          ),
        ],
      ),
    );
  }
}
