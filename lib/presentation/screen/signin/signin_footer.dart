import 'package:com_common/common.dart';
import 'package:com_common/core/common_shared_preferences.dart';
import 'package:com_common/presentation/locale/locale_cubit.dart';
import 'package:com_uikit/uikit.dart';
import 'package:flutter/material.dart';
import 'package:myapp/di/dependency_injection.dart' as di;
import 'package:myapp/gen/assets.gen.dart';
import 'package:myapp/l10n/gen/app_localizations.dart';

class SigninFooter extends StatefulWidget {
  const SigninFooter({super.key});

  @override
  State<SigninFooter> createState() => _SigninFooterState();
}

class _SigninFooterState extends State<SigninFooter> {
  final appSharedPreferences = di.sl<AppCommonSharedPreferences>();
  late bool _isEnglish = false;
  late String currentVersion = '';

  @override
  void initState() {
    super.initState();

    _loadData();
  }

  _loadData() async {
    appSharedPreferences.setOnboarded(true);
    // currentVersion = await GetVersion.projectVersion;
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    currentVersion = packageInfo.version;
    final storedLocale = await appSharedPreferences.getLocale().timeout(
              const Duration(seconds: 2),
              onTimeout: () => 'en',
            ) ??
        '';

    setState(() {
      _isEnglish = storedLocale == 'en';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Column(
            children: [
              // _buildButton(),
              Row(
                children: [
                  _buildLanguageIcon(Assets.icons.icUs, true, () {
                    BlocProvider.of<LocaleCubit>(context, listen: false)
                        .setLocale(const Locale('en'));
                    appSharedPreferences.setLocale('en');
                    setState(() {
                      _isEnglish = true;
                    });
                  }),
                  SizedBox(
                    width: 10.w,
                  ),
                  _buildLanguageIcon(Assets.icons.icVi, false, () {
                    BlocProvider.of<LocaleCubit>(context, listen: false)
                        .setLocale(const Locale('vi'));
                    appSharedPreferences.setLocale('vi');
                    setState(() {
                      _isEnglish = false;
                    });
                  }),
                ],
              ),
              SizedBox(
                height: 10.h,
              ),
              Text(
                AppLocalizations.of(context)!.lbl_version(currentVersion),
                style: ThemeProvider.instance.textStyleMed14
                    .copyWith(color: darkGrey, fontWeight: FontWeight.normal),
                textAlign: TextAlign.center,
              ),
              // Text(
              //   AppLocalizations.of(context)!.lbl_copyright,
              //   textAlign: TextAlign.center,
              // ),
              SizedBox(
                height: 20.h,
              ),
            ],
          ),
        ],
      ),
    );
  }

  _buildLanguageIcon(String icon, bool isActive, VoidCallback callback) {
    return GestureDetector(
      onTap: () => callback.call(),
      child: AbsorbPointer(
        child: Row(
          children: [
            // Radio(
            //     value: _isEnglish,
            //     fillColor: MaterialStateColor.resolveWith(
            //         (states) => ThemeProvider.instance.primaryColor),
            //     groupValue: isActive,
            //     onChanged: (value) {}),
            Container(
              width: 26.w,
              height: 26.w,
              decoration: BoxDecoration(
                  border: Border.all(
                      color: isActive == _isEnglish
                          ? ThemeProvider.instance.primaryColor
                          : lightGrey),
                  borderRadius: BorderRadius.circular(13.w)),
              child: Padding(
                padding: EdgeInsets.all(2.w),
                child: SvgPicture.asset(
                  icon,
                  width: 24.w,
                  height: 24.w,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
