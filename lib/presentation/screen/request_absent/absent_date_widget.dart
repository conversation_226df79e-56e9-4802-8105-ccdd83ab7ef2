import 'package:com_common/common.dart';
import 'package:collection/collection.dart';
import 'package:com_common/presentation/locale/locale_cubit.dart';
import 'package:com_uikit/uikit.dart';
import 'package:com_uikit/utils/constant.dart';
import 'package:com_uikit/widget/dialog/common_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:myapp/constants/constant.dart';
import 'package:myapp/data/model/absent_reason_response/absent_reason_response.dart';
import 'package:myapp/data/model/absent_response/absent_response.dart';
import 'package:myapp/data/model/bus_absence_request/bus_absence_request.dart';
import 'package:myapp/data/model/bus_absence_response/bus_absence_response.dart';
import 'package:myapp/data/model/schedule_response/schedule_model.dart';
import 'package:myapp/gen/assets.gen.dart';
import 'package:myapp/l10n/gen/app_localizations.dart';
import 'package:myapp/presentation/cubit/absent/request_absent_cubit.dart';
import 'package:myapp/presentation/cubit/bus/submit_bus_absence_cubit.dart';
import 'package:myapp/presentation/cubit/change_student/change_student_cubit.dart';
import 'package:myapp/presentation/cubit/schedule/schedule_cubit.dart';
import 'package:myapp/presentation/cubit/schedule/schedule_state.dart';
import 'package:myapp/presentation/cubit/signin/signin_cubit.dart';
import 'package:myapp/presentation/screen/bus/bus_register_type.dart';
import 'package:myapp/presentation/screen/request_absent/absent_model.dart';
import 'package:myapp/presentation/screen/request_absent/reason_dropdown_widget.dart';
import 'package:myapp/presentation/screen/request_absent/session_dropdown_widget.dart';

class AbsentDateWidget extends StatefulWidget {
  final List<AbsentReasonResponse> reasonList;
  final AbsentType? type;
  final AbsentResponse? data;
  final BusAbsenceResponse? bus;
  const AbsentDateWidget({super.key, required this.reasonList, this.data, this.type, this.bus});

  @override
  State<AbsentDateWidget> createState() => _AbsentDateWidgetState();
}

class _AbsentDateWidgetState extends State<AbsentDateWidget> with TickerProviderStateMixin {
  late bool isEditFlow = widget.data != null;
  late TabController tabController = TabController(length: 4, vsync: this, initialIndex: _type.tabIndex);
  late final TextEditingController _fromDateController = TextEditingController()
    ..text = DateFormat('dd/MM/yyyy').format(fromDate);
  late final TextEditingController _toDateController = TextEditingController()
    ..text = DateFormat('dd/MM/yyyy').format(toDate);
  late final TextEditingController _noteController = TextEditingController()
    ..text = widget.data?.ghiChu ?? widget.bus?.lyDoNghi ?? '';
  late AbsentType _type = widget.type ??
      (widget.bus != null
          ? AbsentType.bus
          : absenttypeValues.map?[widget.data?.appXinnghiTypeId.toString()] ?? widget.type ?? AbsentType.byDay);
  late DateTime fromDate = widget.data?.ngayNghiFrom ?? widget.bus?.ngayNghiFrom ?? DateTime.now();
  late DateTime toDate = widget.data?.ngayNghiTo ?? widget.bus?.ngayNghiTo ?? DateTime.now();

  late AbsentReasonResponse? selectedReason =
      widget.reasonList.firstWhereOrNull((element) => element.id == widget.data?.appXinnghiLydoId) ??
          widget.reasonList.firstOrNull;
  late int? startLession =
      (widget.data?.tietNghiFrom != null && widget.data?.tietNghiFrom != 0) ? widget.data!.tietNghiFrom! - 1 : null;
  late int? endLession =
      (widget.data?.tietNghiTo != null && widget.data?.tietNghiTo != 0) ? widget.data!.tietNghiTo! - 1 : null;
  late DateSession? fromTime = dateSessionValues.map?[widget.data?.buoiNghiFrom.toString()] ?? DateSession.morning;
  late DateSession? toTime = dateSessionValues.map?[widget.data?.buoiNghiTo.toString()] ?? DateSession.afternoon;
  late bool isValided = _noteController.text.isNotEmpty || _type == AbsentType.bus;
  final List<ScheduleModel> lesssons = [];

  late BusRegisterType? busAbsenceType =
      busRegisterTypeIntValues.map?[widget.bus?.busXinnghiBuoiId.toString()] ?? BusRegisterType.all;

  bool isAbsenceOnwardBus = true;
  bool isAbsenceReturnBus = true;
  bool isAbsenceDining = true;

  @override
  void initState() {
    super.initState();

    _noteController.addListener(() {
      final newValue = _noteController.text.isNotEmpty;
      debugPrint("startLession: $startLession -- endLession: $endLession");
      if (isValided != newValue) {
        setState(() {
          isValided = newValue;
        });
      }
    });

    if (_type == AbsentType.byLession) {
      BlocProvider.of<ScheduleCubit>(context).getSchedule(
          (BlocProvider.of<ChangeStudentCubit>(context).currentStudent?.id ??
                  BlocProvider.of<SignInCubit>(context).storedResponse?.students?.firstOrNull?.id) ??
              0,
          fromDate.millisecondsSinceEpoch ~/ 1000);
    }
  }

  @override
  void dispose() {
    // _noteController.removeListener(() {});
    _noteController.dispose();
    _fromDateController.dispose();
    _toDateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _buildCalendar(context);
  }

  _buildCalendar(BuildContext context) {
    switch (_type) {
      case AbsentType.byLession:
        return _buildLessonCalendar(context);
      case AbsentType.bySession:
        return _buildRangeWithTimeCalendar(context);
      case AbsentType.bus:
        return _buildBusCalendar(context);

      default:
        return _buildDayCalendar(context);
    }
  }

  _buildDayCalendar(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildType(context),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Text(
            AppLocalizations.of(context)!.lbl_from_date,
          ),
        ),
        Container(
          decoration: BoxDecoration(
              color: HexColor.fromHex(backgroundColorLightBlue),
              borderRadius: BorderRadius.circular(kSmallInputBorderRadius)),
          child: CommonInputTextFormField(
            controller: _fromDateController,
            // readOnly: true,
            height: 46.h,
            inputDecoration: dateTimeInputDecoration(AppLocalizations.of(context)!.lbl_select_date,
                borderColor: Colors.transparent,
                // radius: kSmallInputBorderRadius,
                suffixIcon: IconButton(
                    onPressed: () => _selectDate(context),
                    icon: const Icon(Icons.calendar_month_outlined, color: Colors.black))),
            hint: AppLocalizations.of(context)!.lbl_select_date,

            onTap: () {
              _selectDate(context);
            },
          ),
        ),
        SizedBox(
          height: 8.h,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Text(AppLocalizations.of(context)!.lbl_to_date),
        ),
        Container(
          decoration: BoxDecoration(
              color: HexColor.fromHex(backgroundColorLightBlue),
              borderRadius: BorderRadius.circular(kSmallInputBorderRadius)),
          child: CommonInputTextFormField(
            controller: _toDateController,
            // readOnly: true,
            height: 46.h,
            inputDecoration: dateTimeInputDecoration(AppLocalizations.of(context)!.lbl_select_date,
                borderColor: Colors.transparent,
                // radius: kSmallInputBorderRadius,
                suffixIcon: IconButton(
                    onPressed: () => _selectToDate(context),
                    icon: const Icon(Icons.calendar_month_outlined, color: Colors.black))),
            hint: AppLocalizations.of(context)!.lbl_select_date,
            onTap: () {
              _selectToDate(context);
            },
          ),
        ),
        _buildFotter(context)
      ],
    );
  }

  _buildLessonCalendar(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildType(context),

        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Text(AppLocalizations.of(context)!.lbl_from_date),
        ),
        Container(
          decoration: BoxDecoration(
              color: HexColor.fromHex(backgroundColorLightBlue),
              borderRadius: BorderRadius.circular(kSmallInputBorderRadius)),
          child: CommonInputTextFormField(
            controller: _fromDateController,
            // readOnly: true,
            height: 46.h,
            inputDecoration: dateTimeInputDecoration(AppLocalizations.of(context)!.lbl_select_date,
                borderColor: Colors.transparent,
                // radius: kSmallInputBorderRadius,
                suffixIcon: IconButton(
                    onPressed: () => _selectDate(context),
                    icon: const Icon(Icons.calendar_month_outlined, color: Colors.black))),
            hint: AppLocalizations.of(context)!.lbl_select_date,
            onTap: () {
              _selectDate(context);
            },
          ),
        ),
        // Padding(
        //   padding: EdgeInsets.symmetric(vertical: 8.h),
        //   child: _buildType(context),
        // ),
        SizedBox(
          height: 8.h,
        ),
        Text(AppLocalizations.of(context)!.lbl_select_period),
        SizedBox(
          height: 8.h,
        ),
        _buildLession(context),
        _buildFotter(context)
      ],
    );
  }

  _buildBusCalendar(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildType(context),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Text(AppLocalizations.of(context)!.lbl_from_date),
        ),
        Container(
          decoration: BoxDecoration(
              color: HexColor.fromHex(backgroundColorLightBlue),
              borderRadius: BorderRadius.circular(kSmallInputBorderRadius)),
          child: CommonInputTextFormField(
            controller: _fromDateController,
            // readOnly: true,
            height: 46.h,
            inputDecoration: dateTimeInputDecoration(AppLocalizations.of(context)!.lbl_select_date,
                borderColor: Colors.transparent,
                // radius: kSmallInputBorderRadius,
                suffixIcon: IconButton(
                    onPressed: () => _selectDate(context),
                    icon: const Icon(Icons.calendar_month_outlined, color: Colors.black))),
            hint: AppLocalizations.of(context)!.lbl_select_date,

            onTap: () {
              _selectDate(context);
            },
          ),
        ),
        SizedBox(
          height: 8.h,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Text(AppLocalizations.of(context)!.lbl_to_date),
        ),
        Container(
          decoration: BoxDecoration(
              color: HexColor.fromHex(backgroundColorLightBlue),
              borderRadius: BorderRadius.circular(kSmallInputBorderRadius)),
          child: CommonInputTextFormField(
            controller: _toDateController,
            // readOnly: true,
            height: 46.h,
            inputDecoration: dateTimeInputDecoration(AppLocalizations.of(context)!.lbl_select_date,
                borderColor: Colors.transparent,
                // radius: kSmallInputBorderRadius,
                suffixIcon: IconButton(
                    onPressed: () => _selectToDate(context),
                    icon: const Icon(Icons.calendar_month_outlined, color: Colors.black))),
            hint: AppLocalizations.of(context)!.lbl_select_date,
            onTap: () {
              _selectToDate(context);
            },
          ),
        ),
        SizedBox(
          height: 8.h,
        ),
        _buildBusSession(context),
        SizedBox(height: 16.h),
        _buildEditor(context),
        // SizedBox(height: 16.h),
        Container(
          alignment: Alignment.centerLeft,
          child: Text(
            AppLocalizations.of(context)!.lbl_absent_note_detail,
            style: ThemeProvider.instance.textStyleMed12.copyWith(color: redBorder),
          ),
        ),
        SizedBox(height: 16.h),
        _buildButton(context)
      ],
    );
  }

  _buildBusSession(BuildContext context) {
    return BlocConsumer<ScheduleCubit, ScheduleState>(listener: (context, state) {
      state.maybeWhen(
        orElse: () {},
        success: (list) {
          lesssons.clear();
          lesssons.addAll(list);
          startLession = 0;
          endLession = lesssons.length - 1;
        },
      );
    }, builder: (context, state) {
      return StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
        return ListView.builder(
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: BusRegisterType.values.length,
          itemBuilder: (context, index) {
            return GestureDetector(
                onTap: () {
                  setState(() {});
                },
                child: RadioListTile(
                  contentPadding: EdgeInsets.zero,
                  visualDensity: const VisualDensity(horizontal: 0, vertical: -4),
                  value: BusRegisterType.values[index],
                  // groupValue: Text(list[index],
                  title: Text(
                    BusRegisterType.values[index].name(context),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                    softWrap: true,
                  ),
                  onChanged: (value) {
                    setState(() {
                      busAbsenceType = value;
                    });
                  },
                  groupValue: busAbsenceType,
                ));
          },
        );
      });
    });
  }

  _buildFotter(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: 8.h),
          alignment: Alignment.centerLeft,
          child: Text(
            AppLocalizations.of(context)!.lbl_select_reason,
            style: ThemeProvider.instance.textStyleBold14,
          ),
        ),
        SizedBox(
          height: 5.h,
        ),
        Row(
          children: [
            Expanded(
                child: ReasonDropDownWidget(
              reasonList: widget.reasonList,
              callback: (value) {
                selectedReason = value;
              },
            )),
          ],
        ),
        if (_type != AbsentType.byDay) _buildAdditionInformation(context),
        SizedBox(height: 16.h),
        _buildEditor(context),
        Container(
          alignment: Alignment.centerLeft,
          child: Text(
            AppLocalizations.of(context)!.lbl_absent_note_detail,
            style: ThemeProvider.instance.textStyleMed12.copyWith(color: redBorder),
          ),
        ),
        SizedBox(height: 16.h),
        _buildButton(context)
      ],
    );
  }

  _buildLession(BuildContext context) {
    return BlocConsumer<ScheduleCubit, ScheduleState>(listener: (context, state) {
      state.maybeWhen(
        orElse: () {},
        success: (list) {
          lesssons.clear();
          lesssons.addAll(list);
          startLession = 0;
          endLession = lesssons.length - 1;
        },
      );
    }, builder: (context, state) {
      return StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
        return ListView.builder(
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: lesssons.length,
          itemBuilder: (context, index) {
            return GestureDetector(
              onTap: () {
                setState(() {
                  if (startLession != null && endLession != null) {
                    if (startLession == endLession) {
                      if (startLession! > index) {
                        endLession = startLession;
                        startLession = index;
                        debugPrint('START > index START: $startLession --> END: $endLession');
                      } else {
                        endLession = index;
                        debugPrint('=== START: $startLession --> END: $endLession');
                      }
                    } else {
                      startLession = index;
                      endLession = index;
                      debugPrint('ELSE START: $startLession --> END: $endLession');
                    }
                  }
                });
              },
              child: AbsorbPointer(
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 4.0.h),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          SizedBox(
                              height: 25.h,
                              child: Checkbox(
                                  value: (index >= (startLession ?? 0) && index <= (endLession ?? 0)),
                                  onChanged: ((value) {}))),
                          Expanded(
                            child: Text(
                              lesssons[index].absentDesc(BlocProvider.of<LocaleCubit>(context).isVietnamese),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 2,
                              softWrap: true,
                            ),
                          )
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      });
    });
  }

  _buildAdditionInformation(BuildContext context) {
    return StatefulBuilder(builder: (BuildContext context, StateSetter setLocalState) {
      return ListView(
        padding: EdgeInsets.zero,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        children: [
          SizedBox(height: 16.h),
          Text(
            AppLocalizations.of(context)!.lbl_additional_information,
            style: ThemeProvider.instance.textStyleBold14,
          ),
          SizedBox(height: 4),
          GestureDetector(
            onTap: () {
              if (_type == AbsentType.byDay) {
                return;
              }
              setLocalState(() {
                isAbsenceDining = !isAbsenceDining;
              });
            },
            child: AbsorbPointer(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 4.0.h),
                child: Column(
                  children: [
                    Row(
                      children: [
                        SizedBox(height: 25.h, child: Checkbox(value: isAbsenceDining, onChanged: ((value) {}))),
                        Expanded(
                          child: Text(
                            AppLocalizations.of(context)!.lbl_absence_dinning,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                            softWrap: true,
                          ),
                        )
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          SizedBox(height: 4),
          GestureDetector(
            onTap: () {
              if (_type == AbsentType.byDay) {
                return;
              }
              setLocalState(() {
                isAbsenceOnwardBus = !isAbsenceOnwardBus;
              });
            },
            child: AbsorbPointer(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 4.0.h),
                child: Column(
                  children: [
                    Row(
                      children: [
                        SizedBox(height: 25.h, child: Checkbox(value: isAbsenceOnwardBus, onChanged: ((value) {}))),
                        Expanded(
                          child: Text(
                            AppLocalizations.of(context)!.lbl_absence_onward_bus,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                            softWrap: true,
                          ),
                        )
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          SizedBox(height: 4),
          GestureDetector(
            onTap: () {
              if (_type == AbsentType.byDay) {
                return;
              }
              setLocalState(() {
                isAbsenceReturnBus = !isAbsenceReturnBus;
              });
            },
            child: AbsorbPointer(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 4.0.h),
                child: Column(
                  children: [
                    Row(
                      children: [
                        SizedBox(height: 25.h, child: Checkbox(value: isAbsenceReturnBus, onChanged: ((value) {}))),
                        Expanded(
                          child: Text(
                            AppLocalizations.of(context)!.lbl_absence_reuturn_bus,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                            softWrap: true,
                          ),
                        )
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      );
    });
  }

  _buildRangeWithTimeCalendar(BuildContext context) {
    return Column(
      children: [
        _buildType(context),
        Row(
          children: [
            Expanded(
              flex: 1,
              child: Container(
                height: 30.h,
                padding: EdgeInsets.only(left: 5.h),
                alignment: AlignmentDirectional.centerStart,
                child: Text(
                  AppLocalizations.of(context)!.lbl_select_session,
                ),
              ),
            ),
            Container(
              width: 30.w,
              alignment: AlignmentDirectional.center,
            ),
            Expanded(
              flex: 1,
              child: Container(
                  padding: EdgeInsets.only(left: 5.h),
                  height: 30.h,
                  alignment: AlignmentDirectional.centerStart,
                  child: Text(AppLocalizations.of(context)!.lbl_from_date)),
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
                child: SessionDropDownWidget(
              initialValue: fromTime ?? DateSession.morning,
              callBack: (value) {
                fromTime = value;
              },
            )),
            Container(width: 30.w, alignment: AlignmentDirectional.center, child: const Text('-')),
            Expanded(
              child: Column(
                children: [
                  Container(
                    decoration: BoxDecoration(
                        color: HexColor.fromHex(backgroundColorLightBlue),
                        borderRadius: BorderRadius.circular(kSmallInputBorderRadius)),
                    child: CommonInputTextFormField(
                      controller: _fromDateController,
                      height: 48,
                      inputDecoration: dateTimeInputDecoration(AppLocalizations.of(context)!.lbl_dob,
                          borderColor: Colors.transparent,
                          // radius: kSmallInputBorderRadius,
                          suffixIcon: IconButton(
                              onPressed: () => _selectDate(context),
                              icon: const Icon(Icons.calendar_month_outlined, color: Colors.black))),
                      hint: AppLocalizations.of(context)!.lbl_dob,
                      onTap: () {
                        _selectDate(context);
                      },
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
        Row(
          children: [
            const Expanded(
              child: SizedBox(),
            ),
            Container(
              width: 30.w,
              alignment: AlignmentDirectional.center,
            ),
            Expanded(
              child: Container(
                  padding: EdgeInsets.only(left: 5.h),
                  height: 30.h,
                  alignment: AlignmentDirectional.centerStart,
                  child: Text(AppLocalizations.of(context)!.lbl_to_date)),
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
                child: SessionDropDownWidget(
              initialValue: toTime ?? DateSession.afternoon,
              callBack: (value) {
                toTime = value;
              },
            )),
            Container(width: 30.w, alignment: AlignmentDirectional.center, child: const Text('-')),
            Expanded(
              child: Column(
                children: [
                  Container(
                    decoration: BoxDecoration(
                        color: HexColor.fromHex(backgroundColorLightBlue),
                        borderRadius: BorderRadius.circular(kSmallInputBorderRadius)),
                    child: CommonInputTextFormField(
                      controller: _toDateController,
                      inputDecoration: dateTimeInputDecoration(AppLocalizations.of(context)!.lbl_dob,
                          borderColor: Colors.transparent,
                          // radius: kSmallInputBorderRadius,
                          suffixIcon: IconButton(
                              onPressed: () => _selectToDate(context),
                              icon: const Icon(Icons.calendar_month_outlined, color: Colors.black))),
                      hint: AppLocalizations.of(context)!.lbl_dob,
                      onTap: () {
                        _selectToDate(context);
                      },
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
        _buildFotter(context)
      ],
    );
  }

  _buildType(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
      child: TabBar(
        controller: tabController,
        isScrollable: true,
        labelPadding: EdgeInsets.symmetric(horizontal: 12.w),
        indicator:
            BoxDecoration(color: ThemeProvider.instance.secondaryColor, borderRadius: BorderRadius.circular(10.r)),
        unselectedLabelColor: Colors.grey,
        // indicatorColor: ThemeProvider.instance.primaryColor,
        onTap: (value) {
          final newValue = absenttypeValues.map?[value.toString()] ?? AbsentType.byDay;

          if (_type != newValue) {
            if (newValue == AbsentType.byLession) {
              BlocProvider.of<ScheduleCubit>(context).getSchedule(
                  (BlocProvider.of<ChangeStudentCubit>(context).currentStudent?.id ??
                          BlocProvider.of<SignInCubit>(context).storedResponse?.students?.firstOrNull?.id) ??
                      0,
                  fromDate.millisecondsSinceEpoch ~/ 1000);
            }
            setState(() {
              _type = newValue;
              isValided = _noteController.text.isNotEmpty || _type == AbsentType.bus;
            });
          }
        },
        labelColor: white,
        tabs: [
          Tab(
            height: 40.h,
            text: AppLocalizations.of(context)!.lbl_absent_by_bus.toUpperCase(),
          ),
          Tab(
            height: 40.h,
            text: AppLocalizations.of(context)!.lbl_absent_by_day.toUpperCase(),
          ),
          Tab(
            height: 40.h,
            text: AppLocalizations.of(context)!.lbl_absent_by_period.toUpperCase(),
          ),
          Tab(
            height: 40.h,
            text: AppLocalizations.of(context)!.lbl_absent_by_session.toUpperCase(),
          ),
        ],
      ),
    );
  }

  _selectDate(BuildContext context) async {
    debugPrint(BlocProvider.of<LocaleCubit>(context).appLocale.toString());

    final now = DateTime.now();
    showCupertinoDatePickerDialog(
        context: context,
        initialDateTime: fromDate,
        minimumDate: DateTime(now.year, now.month, now.day, 0, 0),
        mode: CupertinoDatePickerMode.date,
        onCompled: (newSelectedDate) {
          setState(() {
            fromDate = newSelectedDate;
            _fromDateController
              ..text = DateFormat('dd/MM/yyyy').format(newSelectedDate)
              ..selection = TextSelection.fromPosition(
                  TextPosition(offset: _fromDateController.text.length, affinity: TextAffinity.upstream));

            if (newSelectedDate.isAfter(toDate)) {
              debugPrint(
                  '${DateFormat('dd/MM/yyyy').format(newSelectedDate)} is after ${DateFormat('dd/MM/yyyy').format(toDate)}');
              toDate = newSelectedDate;
              _toDateController
                ..text = DateFormat('dd/MM/yyyy').format(newSelectedDate)
                ..selection = TextSelection.fromPosition(
                    TextPosition(offset: _toDateController.text.length, affinity: TextAffinity.upstream));
            }
          });
          if (_type == AbsentType.byLession && mounted) {
            BlocProvider.of<ScheduleCubit>(context).getSchedule(
                (BlocProvider.of<ChangeStudentCubit>(context).currentStudent?.id ??
                        BlocProvider.of<SignInCubit>(context).storedResponse?.students?.firstOrNull?.id) ??
                    0,
                fromDate.millisecondsSinceEpoch ~/ 1000);
          }
        });
    debugPrint('Change From Date');

    FocusManager.instance.primaryFocus?.unfocus();
  }

  _selectToDate(BuildContext context) async {
    showCupertinoDatePickerDialog(
        context: context,
        initialDateTime: toDate,
        minimumDate: fromDate,
        mode: CupertinoDatePickerMode.date,
        onCompled: (newSelectedDate) {
          setState(() {
            toDate = newSelectedDate;
            _toDateController
              ..text = DateFormat('dd/MM/yyyy').format(newSelectedDate)
              ..selection = TextSelection.fromPosition(
                  TextPosition(offset: _toDateController.text.length, affinity: TextAffinity.upstream));

            if (newSelectedDate.isBefore(fromDate)) {
              debugPrint(
                  '${DateFormat('dd/MM/yyyy').format(newSelectedDate)} is before ${DateFormat('dd/MM/yyyy').format(fromDate)}');
              fromDate = newSelectedDate;
              _fromDateController
                ..text = DateFormat('dd/MM/yyyy').format(newSelectedDate)
                ..selection = TextSelection.fromPosition(
                    TextPosition(offset: _fromDateController.text.length, affinity: TextAffinity.upstream));
            }
          });
        });

    FocusManager.instance.primaryFocus?.unfocus();
  }

  _buildEditor(BuildContext context) {
    return Container(
        decoration: BoxDecoration(
            color: HexColor.fromHex(backgroundColorLightBlue),
            borderRadius: BorderRadius.circular(kSmallInputBorderRadius)),
        margin: EdgeInsets.only(bottom: 15.h),
        child: Column(
          children: [
            Container(
                padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 15.w),
                child: ConstrainedBox(
                  constraints: BoxConstraints(minHeight: 150.h, maxHeight: 300.h),
                  child: TextField(
                    controller: _noteController,
                    decoration: InputDecoration(
                      hintText: '${AppLocalizations.of(context)!.lbl_note}...',
                      focusedBorder: InputBorder.none,
                      enabledBorder: InputBorder.none,
                    ),
                    keyboardType: TextInputType.multiline,
                    textInputAction: TextInputAction.done,
                    minLines: 1,
                    maxLines: 5,
                  ),
                ))
          ],
        ));
  }

  _buildButton(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          height: 50,
          child: CustomButton(
              isDisable: !isValided,
              text: AppLocalizations.of(context)!.lbl_submit.toUpperCase(),
              colorBackground: ThemeProvider.instance.primaryColor,
              cornerRadius: kBigInputBorderRadius.h,
              titleStyle: ThemeProvider.instance.textStyleBold18.copyWith(color: Colors.white),
              onPressed: () {
                showDialog(context);
              }),
        ),
        SizedBox(
          height: 20.h,
        ),
      ],
    );
  }

  String get confirmMessage {
    String additionMessage = '';
    switch (_type) {
      case AbsentType.bySession:
        additionMessage =
            '${AppLocalizations.of(context)!.lbl_from.toCapitalize()} ${fromTime?.stringValue(context)} ${fromDate.toCommonString()} ${AppLocalizations.of(context)!.lbl_to} ${toTime?.stringValue(context)} ${toDate.toCommonString()}';
        break;
      case AbsentType.byLession:
        if (startLession == endLession) {
          additionMessage =
              '${AppLocalizations.of(context)!.lbl_period} ${(startLession ?? 0) + 1} ${fromDate.toCommonString()}';
        } else {
          additionMessage =
              '${AppLocalizations.of(context)!.lbl_from.toCapitalize()} ${AppLocalizations.of(context)!.lbl_period} ${(startLession ?? 0) + 1} ${AppLocalizations.of(context)!.lbl_to} ${AppLocalizations.of(context)!.lbl_period} ${(endLession ?? 0) + 1}  ${fromDate.toCommonString()}';
        }
        break;
      default:
        if (fromDate.day == toDate.day) {
          additionMessage = fromDate.toCommonString();
        } else {
          additionMessage =
              '${AppLocalizations.of(context)!.lbl_from.toCapitalize()} ${fromDate.toCommonString()} ${AppLocalizations.of(context)!.lbl_to} ${toDate.toCommonString()}';
        }
        break;
    }

    return AppLocalizations.of(context)!.lbl_absent_confirm(additionMessage);
  }

  Future<void> showDialog(BuildContext context) async {
    final dialogOption = DialogOption(
      title: AppLocalizations.of(context)!.lbl_leave_application,
      message: confirmMessage,
      networkImage: UIKITImageResource(isNetworkImage: false, localPath: Assets.images.icPandaRocket.path),
      iconSize: const Size(150, 150),
      buttonTitle: AppLocalizations.of(context)!.lbl_confirm,
      buttonSubTitle: AppLocalizations.of(context)!.lbl_cancel,
    );
    final result = await DialogManager().showDialogWithOption(context, dialogOption);
    if (result != null && mounted) {
      submit();
    }
  }

  submit() {
    if (_type == AbsentType.bus) {
      final studentId = (BlocProvider.of<ChangeStudentCubit>(context).currentStudent?.id ??
              BlocProvider.of<SignInCubit>(context).storedResponse?.students?.firstOrNull?.id) ??
          0;
      final request = BusAbsenceRequest(
        id: widget.bus?.id ?? 0,
        tsHocsinhId: studentId,
        ngayNghiFrom: fromDate.millisecondsSinceEpoch ~/ 1000,
        ngayNghiTo: toDate.millisecondsSinceEpoch ~/ 1000,
        lyDoNghi: _noteController.text,
        busXinnghiBuoiId: busAbsenceType?.intValue ?? 0,
      );
      BlocProvider.of<SubmitBusAbsenceCubit>(context).submit(request: request);
    } else {
      final studentId = (BlocProvider.of<ChangeStudentCubit>(context).currentStudent?.id ??
              BlocProvider.of<SignInCubit>(context).storedResponse?.students?.firstOrNull?.id) ??
          0;
      BlocProvider.of<RequestAbsentCubit>(context).submit(
          id: widget.data?.id,
          studentId: studentId,
          type: _type.value,
          startDate: fromDate.millisecondsSinceEpoch ~/ 1000,
          endDate: toDate.millisecondsSinceEpoch ~/ 1000,
          startSession: fromTime?.value ?? 0,
          endSession: toTime?.value ?? 0,
          startLession: (startLession != null) ? (startLession! + 1) : 0,
          endLession: (endLession != null) ? (endLession! + 1) : 0,
          note: _noteController.text,
          reasonId: selectedReason?.id ?? 0,
          isEditFlow: widget.data != null);
    }
  }
}
