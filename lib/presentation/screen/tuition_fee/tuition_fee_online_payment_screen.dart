import 'package:com_common/constant/style_input.dart';
import 'package:com_common/core/environment_configuration.dart';
import 'package:com_common/ulti/dialog_manager.dart';
import 'package:com_uikit/uikit.dart';
import 'package:flutter/material.dart';
import 'package:myapp/gen/assets.gen.dart';
import 'package:myapp/l10n/gen/app_localizations.dart';
import 'package:myapp/presentation/screen/tuition_fee/tuition_fee_detail_screen.dart';
import 'package:webview_flutter/webview_flutter.dart';
// Import for Android features.
import 'package:webview_flutter_android/webview_flutter_android.dart';
// Import for iOS features.
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

class TuitionFeeOnlinePaymentScreen extends StatefulWidget {
  static const String routeName = '/tuition_fee_online_payment_screen';
  final String url;

  const TuitionFeeOnlinePaymentScreen({
    super.key,
    required this.url,
  });

  @override
  State<TuitionFeeOnlinePaymentScreen> createState() =>
      _TuitionFeeOnlinePaymentScreenState();
}

class _TuitionFeeOnlinePaymentScreenState
    extends State<TuitionFeeOnlinePaymentScreen> {
  late WebViewController controller = WebViewController()
    ..loadRequest(Uri.parse(widget.url))
    ..setJavaScriptMode(JavaScriptMode.unrestricted)
    ..setBackgroundColor(white)
    ..setNavigationDelegate(
      NavigationDelegate(
        onProgress: (int progress) {},
        onPageStarted: (String url) {},
        onPageFinished: (String url) {},
        onWebResourceError: (WebResourceError error) {},
        onNavigationRequest: (NavigationRequest request) {
          if (request.url
              .startsWith(AppConfigModel.instance.onepayPaymentCallback)) {
            final result =
                (Uri.parse(request.url).queryParameters)["is_success"];

            if (result?.toLowerCase() == 'true') {
              final dialogOption = DialogOption(
                title: AppLocalizations.of(context)!.lbl_successfully,
                titleStyle: TitleTextStyle.error,
                message: AppLocalizations.of(context)!.lbl_payment_successfull,
                lottieFile: Assets.lotties.lottieSuccess,
                buttonTitle: AppLocalizations.of(context)!.lbl_ok,
              );
              DialogManager().showDialogWithOption(context, dialogOption)?.then(
                  (value) => Navigator.of(context).popUntil(
                      ModalRoute.withName(TuitionFeeDetailScreen.routeName)));

              return NavigationDecision.navigate;
            } else {
              final dialogOption = DialogOption(
                title: AppLocalizations.of(context)!.lbl_oops,
                titleStyle: TitleTextStyle.error,
                networkImage: UIKITImageResource(
                    isNetworkImage: false,
                    localPath: Assets.images.imgOnlinePayment.path),
                iconSize: const Size(150, 150),
                message: AppLocalizations.of(context)!.lbl_payment_failed,
                // lottieFile: Assets.lotties.lottieError,
                buttonTitle: AppLocalizations.of(context)!.lbl_ok,
              );
              DialogManager()
                  .showDialogWithOption(context, dialogOption)
                  ?.then((value) => Navigator.of(context).pop());
              return NavigationDecision.navigate;
            }
          } else if (request.url.startsWith('https://www.youtube.com/')) {
            return NavigationDecision.prevent;
          }
          return NavigationDecision.navigate;
        },
      ),
    );

  late final WebViewCookieManager cookieManager = WebViewCookieManager();
  void clearCookies() async {
    await cookieManager.clearCookies();
  }

  @override
  void initState() {
    super.initState();
    clearCookies();
    late final PlatformWebViewControllerCreationParams params;
    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }

    final WebViewController controller =
        WebViewController.fromPlatformCreationParams(params);
// ···
    if (controller.platform is AndroidWebViewController) {
      AndroidWebViewController.enableDebugging(true);
      (controller.platform as AndroidWebViewController)
          .setMediaPlaybackRequiresUserGesture(false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: false,
      extendBody: true,
      appBar: WidgetAppBar(
          title: AppLocalizations.of(context)!.lbl_online_payment,
          elevation: 1,
          hideBack: false,
          onClickIconLeft: () {
            Navigator.of(context).pop();
          }),
      body: SafeArea(child: WebViewWidget(controller: controller)),
    );
  }
}
