import 'dart:io';

import 'package:com_common/common.dart';
import 'package:com_uikit/uikit.dart';
import 'package:com_common/core/common_shared_preferences.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart' as image_picker;
import 'package:myapp/l10n/gen/app_localizations.dart';
import 'package:myapp/presentation/cubit/change_student/change_student_cubit.dart';
import 'package:myapp/presentation/cubit/config/common_state.dart';
import 'package:myapp/presentation/cubit/config/config_cubit.dart';
import 'package:myapp/presentation/cubit/notification/notification_count_cubit.dart';
import 'package:myapp/presentation/cubit/signin/signin_cubit.dart';
import 'package:myapp/presentation/cubit/signin/signout_cubit.dart';
import 'package:myapp/presentation/screen/change_password/change_password_screen.dart';
import 'package:myapp/presentation/screen/contact/contact_screen.dart';
import 'package:myapp/presentation/screen/notifications/notifications_screen.dart';
import 'package:myapp/presentation/screen/profile/avatar/avatar_screen.dart';
import 'package:myapp/presentation/screen/profile/avatar/capture/capture_screen.dart';
import 'package:myapp/presentation/screen/profile/avatar/capture/permission_helper/permission_hander_utils.dart';
import 'package:myapp/presentation/screen/profile/setting_content_widget.dart';
import 'package:myapp/presentation/screen/profile/setting_model.dart';
import 'package:myapp/di/dependency_injection.dart' as di;
import 'package:myapp/presentation/screen/request_support/request_support_list_screen.dart';
import 'package:myapp/presentation/screen/signin/signin_screen.dart';
import 'package:myapp/presentation/screen/student_profile/student_profile_screen.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:url_launcher/url_launcher.dart';

class SettingScreen extends StatefulWidget {
  static const String routeName = '/setting_screen';
  const SettingScreen({
    super.key,
  });

  @override
  _SettingScreenState createState() => _SettingScreenState();
}

class _SettingScreenState extends State<SettingScreen> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;
  final List<SettingModel> settings = [
    // SettingModel.notification,
    SettingModel.information,
    SettingModel.language,
    // SettingModel.security,
    // SettingModel.password,
  ];

  final List<SettingModel> support = [
    SettingModel.useGuide,
    // SettingModel.hotline,
    // SettingModel.support,
    // SettingModel.about,
  ];
  final appSharedPreferences = di.sl<AppCommonSharedPreferences>();
  late final bool _isEnglish = false;
  late bool isEnableBiometric = false;
  late String currentVersion = '';
  late String deviceToken = '';

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        automaticallyImplyLeading: false,
        systemOverlayStyle: Platform.isIOS
            ? SystemUiOverlayStyle.dark
            : const SystemUiOverlayStyle(
                statusBarColor: Colors.transparent,
                statusBarBrightness: Brightness.dark,
                statusBarIconBrightness: Brightness.dark, // status bar icons' color
                systemNavigationBarIconBrightness: Brightness.dark),
        elevation: 0,
        toolbarHeight: 0, // Status bar color
      ),
      body: SafeArea(
        bottom: true,
        top: false,
        child: Stack(
          fit: StackFit.expand,
          children: [
            SettingContentWidget(
                settings: settings,
                support: support,
                onTap: (model) => onTap(context, model),
                onEditAvatar: () => onTapAvatarButton(context)),
            _buildSignOutProgress(context),
          ],
        ),
      ),
    );
  }

  onTap(BuildContext context, SettingModel model) {
    switch (model) {
      case SettingModel.useGuide:
        launchUrl(Uri.parse(BlocProvider.of<GetConfigCubit>(context).appConfig?.userGuideLinks?.url ?? ''));
        break;
      case SettingModel.language:
        break;
      case SettingModel.security:
        break;
      case SettingModel.password:
        Navigator.of(context).pushNamed(ChangePasswordScreen.routeName);
        break;
      case SettingModel.information:
        Navigator.of(context).pushNamed(StudentProfileScreen.routeName);
        break;
      case SettingModel.hotline:
        Navigator.of(context).pushNamed(ContactScreen.routeName, arguments: {
          'index': 1,
        });
        break;
      case SettingModel.support:
        Navigator.of(context).pushNamed(RequestSupportListScreen.routeName);
        break;
      case SettingModel.about:
        break;
      case SettingModel.notification:
        Navigator.of(context).pushNamed(NotificationsScreen.routeName);
        break;
      case SettingModel.appversion:
        break;
      default:
        break;
    }
  }

  _buildSignOutProgress(BuildContext context) {
    return BlocConsumer<SignOutCubit, CommonState>(
      listener: ((context, state) {
        state.maybeWhen(
          error: (error) {
            debugPrint(error.toString());
            dialogUtils(context, error).then((_) => handleDefaultAction(context, error));
          },
          orElse: () {},
          success: (data) {
            BlocProvider.of<ChangeStudentCubit>(context).currentStudent = null;
            BlocProvider.of<SignInCubit>(context).storedResponse = null;
            BlocProvider.of<NotificationCountCubit>(context).reset();

            Navigator.of(context).pushNamedAndRemoveUntil(SignInScreen.routeName, (route) => false);
          },
        );
      }),
      builder: (context, state) {
        return (state == const CommonState.loading())
            ? const Center(child: CircularProgressIndicator())
            : const SizedBox();
      },
    );
  }

  void onTapAvatarButton(BuildContext buildContext) {
    showCupertinoModalPopup(
        context: buildContext,
        builder: (context) {
          return CupertinoActionSheet(
            message: Text(AppLocalizations.of(context)!.lbl_edit_avatar),
            actions: [
              CupertinoActionSheetAction(
                /// This parameter indicates the action would be a default
                /// defualt behavior, turns the action's text to bold text.
                // isDefaultAction: true,
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.of(buildContext)
                      .pushNamed(
                    CaptureScreen.routeName,
                  )
                      .then((value) {
                    if (value != null) {
                      Navigator.of(buildContext).pushNamed(
                        CropImageScreen.routeName,
                        arguments: {'path': value},
                      ).then((value) {
                        if (value != null) {
                          setState(() {});
                        }
                      });
                    }
                  });
                },
                child:
                    Text(AppLocalizations.of(context)!.lbl_from_camera, style: ThemeProvider.instance.textStyleMed18),
              ),
              CupertinoActionSheetAction(
                /// This parameter indicates the action would be a default
                /// defualt behavior, turns the action's text to bold text.
                // isDefaultAction: true,
                onPressed: () async {
                  Navigator.pop(context);
                  var permission = await PhotoManager.requestPermissionExtend();
                  if (permission.isAuth == true) {
                    final pickedFile =
                        await image_picker.ImagePicker().pickImage(source: image_picker.ImageSource.gallery);
                    if (pickedFile != null && mounted) {
                      Navigator.of(buildContext)
                          .pushNamed(CropImageScreen.routeName, arguments: {'path': pickedFile.path}).then((value) {
                        if (value != null) {
                          Future.delayed(const Duration(milliseconds: 500), () {
                            setState(() {});
                          });
                        }
                      });
                    }
                  } else if (mounted) {
                    // PhotoManager.openSetting();
                    showDialogPhotoPermission(context: context);
                  }
                },
                child: Text(
                  AppLocalizations.of(context)!.lbl_from_library,
                  style: ThemeProvider.instance.textStyleMed18,
                ),
              ),
            ],
            cancelButton: CupertinoActionSheetAction(
              /// This parameter indicates the action would perform
              /// a destructive action such as delete or exit and turns
              /// the action's text color to red.

              onPressed: () {
                Navigator.of(context).popAndPushNamed(CaptureScreen.routeName);
              },
              child: Text(AppLocalizations.of(context)!.lbl_cancel,
                  style: ThemeProvider.instance.textStyleMed18.copyWith(color: Colors.red)),
            ),
          );
        });
  }
}
