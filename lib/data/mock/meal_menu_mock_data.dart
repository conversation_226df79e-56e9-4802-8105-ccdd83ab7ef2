import 'package:myapp/data/model/meal_menu_response/meal_menu_response.dart';
import 'package:com_common/core/base_response.dart';
import 'package:myapp/data/model/meal_menu_response/thuc_don.dart';

/// Mock data cho chức năng xem bữa ăn
/// Sử dụng khi API chưa sẵn sàng
class MealMenuMockData {
  /// Mock data cho thực đơn các bữa ăn
  static const List<MealMenuResponse> mockMealMenuList = [
    // Bữa sáng
    MealMenuResponse(
      caAn: 'Bữa sáng',
      caAnEn: 'Breakfast',
      thucDon: [
        ThucDon(
          tenMonAn: 'Phở gà ta',
          tenMonAnEn: 'Chicken Pho',
          calo: 350.5,
          ghiChu: 'Món ăn truyền thống Việt Nam',
        ),
        ThucDon(
          tenMonAn: '<PERSON><PERSON>h mì thịt nướng',
          tenMonAnEn: 'Grilled Pork Banh Mi',
          calo: 280.0,
          ghiChu: '<PERSON><PERSON>h mì Việt Nam với thịt nướng',
        ),
        ThucDon(
          tenMonAn: 'Sữa đậu nành',
          tenMonAnEn: 'Soy Milk',
          calo: 80.0,
          ghiChu: 'Đồ uống bổ dưỡng',
        ),
      ],
      imgUrls: [
        'https://upload.wikimedia.org/wikipedia/commons/thumb/b/b0/C%C6%A1m_T%E1%BA%A5m%2C_Da_Nang%2C_Vietnam.jpg/1200px-C%C6%A1m_T%E1%BA%A5m%2C_Da_Nang%2C_Vietnam.jpg',
        'https://madamelan.vn/storage/mdl-1-1.jpg',
        // 'https://cdn.eva.vn/upload/1-2024/images/2024-03-07/1-1709776568-537-width800height600.jpg'
      ],
    ),

    // Bữa trưa
    MealMenuResponse(
      caAn: 'Bữa trưa',
      caAnEn: 'Lunch',
      thucDon: [
        ThucDon(
          tenMonAn: 'Cơm tấm sài gòn',
          tenMonAnEn: 'Saigon Broken Rice',
          calo: 450.0,
          ghiChu: 'Đặc sản miền Nam với sườn nướng',
        ),
        ThucDon(
          tenMonAn: 'Canh chua cá basa',
          tenMonAnEn: 'Sour Fish Soup',
          calo: 120.0,
          ghiChu: 'Canh chua truyền thống miền Nam',
        ),
        ThucDon(
          tenMonAn: 'Rau muống xào tỏi',
          tenMonAnEn: 'Stir-fried Water Spinach',
          calo: 60.0,
          ghiChu: 'Rau xanh bổ sung vitamin',
        ),
        ThucDon(
          tenMonAn: 'Trái cây tráng miệng',
          tenMonAnEn: 'Fresh Fruits',
          calo: 80.0,
          ghiChu: 'Cam, táo, chuối theo mùa',
        ),
      ],
      imgUrls: [
        'https://upload.wikimedia.org/wikipedia/commons/thumb/b/b0/C%C6%A1m_T%E1%BA%A5m%2C_Da_Nang%2C_Vietnam.jpg/1200px-C%C6%A1m_T%E1%BA%A5m%2C_Da_Nang%2C_Vietnam.jpg',
        'https://madamelan.vn/storage/mdl-1-1.jpg',
        'https://cdn.eva.vn/upload/1-2024/images/2024-03-07/1-1709776568-537-width800height600.jpg',
        'https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEiHOAu1Yu9VJvM-8tMESTUNcUnngYU4tr1jawFjJ3ccUCM57WsB6lajI6jrOPjZrNMxvQGHPm-oCrWtPQRgvrWBVMU7Wm4stAK_fChSlIw8nldd2YMmXAMoW2v1Ig_754MIxODOoPLjkmM/s1600/Bi-quyet-chon-thuc-don-tiec-cuoi-ngon-khoa-hoc-2.jpg'
        // 'https://blogger.googleusercontent.com/img/b/R29vZ2xl/AVvXsEiHOAu1Yu9VJvM-8tMESTUNcUnngYU4tr1jawFjJ3ccUCM57WsB6lajI6jrOPjZrNMxvQGHPm-oCrWtPQRgvrWBVMU7Wm4stAK_fChSlIw8nldd2YMmXAMoW2v1Ig_754MIxODOoPLjkmM/s1600/Bi-quyet-chon-thuc-don-tiec-cuoi-ngon-khoa-hoc-2.jpg'
      ],
    ),

    // Bữa tối
    MealMenuResponse(
      caAn: 'Bữa tối',
      caAnEn: 'Dinner',
      thucDon: [
        ThucDon(
          tenMonAn: 'Chè đậu đen',
          tenMonAnEn: 'Black Bean Sweet Soup',
          calo: 200.0,
          ghiChu: 'Tráng miệng ngọt mát',
        ),
        ThucDon(
          tenMonAn: 'Bánh flan',
          tenMonAnEn: 'Flan Cake',
          calo: 150.0,
          ghiChu: 'Bánh ngọt kiểu Pháp',
        ),
      ],
      imgUrls: [
        'https://upload.wikimedia.org/wikipedia/commons/thumb/b/b0/C%C6%A1m_T%E1%BA%A5m%2C_Da_Nang%2C_Vietnam.jpg/1200px-C%C6%A1m_T%E1%BA%A5m%2C_Da_Nang%2C_Vietnam.jpg',
        // 'https://madamelan.vn/storage/mdl-1-1.jpg',
        // 'https://cdn.eva.vn/upload/1-2024/images/2024-03-07/1-1709776568-537-width800height600.jpg'
      ],
    ),
  ];

  /// Mock data cho ngày khác (ví dụ thứ 2)
  static const List<MealMenuResponse> mockMealMenuListMonday = [
    MealMenuResponse(
      caAn: 'Bữa sáng',
      caAnEn: 'Breakfast',
      thucDon: [
        ThucDon(
          tenMonAn: 'Bún bò Huế',
          tenMonAnEn: 'Hue Beef Noodle Soup',
          calo: 380.0,
          ghiChu: 'Đặc sản miền Trung',
        ),
        ThucDon(
          tenMonAn: 'Chả cá',
          tenMonAnEn: 'Grilled Fish Cake',
          calo: 120.0,
          ghiChu: 'Chả cá thơm ngon',
        ),
      ],
      imgUrls: [
        'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=400',
      ],
    ),
    MealMenuResponse(
      caAn: 'Bữa trưa',
      caAnEn: 'Lunch',
      thucDon: [
        ThucDon(
          tenMonAn: 'Cơm gà Hải Nam',
          tenMonAnEn: 'Hainanese Chicken Rice',
          calo: 420.0,
          ghiChu: 'Cơm gà kiểu Singapore',
        ),
      ],
      imgUrls: [
        'https://images.unsplash.com/photo-1559314809-0f31657def5e?w=400',
      ],
    ),
  ];

  /// Mock data trống (không có bữa ăn)
  static const List<MealMenuResponse> mockEmptyMealMenuList = [];

  /// Tạo BaseResponse thành công với mock data
  static BaseResponse<List<MealMenuResponse>> getMockSuccessResponse() {
    return BaseResponse<List<MealMenuResponse>>(
      mockMealMenuList,
      true,
      'Lấy thực đơn thành công',
    );
  }

  /// Tạo BaseResponse thành công với mock data ngày thứ 2
  static BaseResponse<List<MealMenuResponse>> getMockMondayResponse() {
    return BaseResponse<List<MealMenuResponse>>(
      mockMealMenuListMonday,
      true,
      'Lấy thực đơn thành công',
    );
  }

  /// Tạo BaseResponse trống
  static BaseResponse<List<MealMenuResponse>> getMockEmptyResponse() {
    return BaseResponse<List<MealMenuResponse>>(
      mockEmptyMealMenuList,
      true,
      'Không có thực đơn cho ngày này',
    );
  }

  /// Tạo BaseResponse lỗi
  static BaseResponse<List<MealMenuResponse>> getMockErrorResponse() {
    return BaseResponse<List<MealMenuResponse>>(
      null,
      false,
      'Lỗi kết nối mạng',
    );
  }

  /// Lấy mock data theo ngày (đơn giản)
  static BaseResponse<List<MealMenuResponse>> getMockDataByDate(DateTime date) {
    // Ví dụ logic đơn giản: thứ 2 trả về data khác
    if (date.weekday == DateTime.monday) {
      return getMockMondayResponse();
    }

    // Các ngày khác trả về data mặc định
    return getMockSuccessResponse();
  }

  /// Lấy mock data theo schoolId và dateTime
  static BaseResponse<List<MealMenuResponse>> getMockDataByParams({
    required int schoolId,
    required int dateTime,
  }) {
    // Convert timestamp to DateTime
    final date = DateTime.fromMillisecondsSinceEpoch(dateTime * 1000);

    // Simulate different data for different schools
    if (schoolId == 999) {
      return getMockEmptyResponse();
    }

    return getMockDataByDate(date);
  }
}
