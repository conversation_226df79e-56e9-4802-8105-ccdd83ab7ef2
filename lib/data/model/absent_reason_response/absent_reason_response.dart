import 'package:collection/collection.dart';
import 'package:json_annotation/json_annotation.dart';

part 'absent_reason_response.g.dart';

@JsonSerializable()
class AbsentReasonResponse {
  final int? id;
  final String? name;
  @JsonKey(name: 'name_en')
  final String? nameEn;

  const AbsentReasonResponse({this.id, this.name, this.nameEn});

  String? getName(isVietnamese) {
    return isVietnamese ? name : nameEn;
  }

  @override
  String toString() {
    return 'AbsentReasonResponse(id: $id, name: $name, nameEn: $nameEn)';
  }

  factory AbsentReasonResponse.fromJson(Map<String, dynamic> json) {
    return _$AbsentReasonResponseFromJson(json);
  }

  Map<String, dynamic> toJson() => _$AbsentReasonResponseToJson(this);

  AbsentReasonResponse copyWith({
    int? id,
    String? name,
    String? nameEn,
  }) {
    return AbsentReasonResponse(
      id: id ?? this.id,
      name: name ?? this.name,
      nameEn: nameEn ?? this.nameEn,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! AbsentReasonResponse) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode => id.hashCode ^ name.hashCode ^ nameEn.hashCode;
}
