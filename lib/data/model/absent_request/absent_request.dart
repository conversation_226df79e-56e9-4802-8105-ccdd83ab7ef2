import 'package:collection/collection.dart';
import 'package:json_annotation/json_annotation.dart';

part 'absent_request.g.dart';

@JsonSerializable()
class AbsentRequest {
  final int? id;
  @Json<PERSON><PERSON>(name: 'ts_hocsinh_id')
  final int? tsHocsinhId;
  @Json<PERSON><PERSON>(name: 'app_xinnghi_type_id')
  final int? appXinnghiTypeId;
  @<PERSON>sonKey(name: 'ngay_nghi_from')
  final int? ngayNghiFrom;
  @JsonKey(name: 'ngay_nghi_to')
  final int? ngayNghiTo;
  @Json<PERSON><PERSON>(name: 'tiet_nghi_from')
  final int? tietNghiFrom;
  @Json<PERSON><PERSON>(name: 'tiet_nghi_to')
  final int? tietNghiTo;
  @JsonKey(name: 'buoi_nghi_from')
  final int? buoiNghiFrom;
  @<PERSON>sonKey(name: 'buoi_nghi_to')
  final int? buoiNghiTo;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'app_xinnghi_lydo_id')
  final int? lyDoNghi;
  @<PERSON>son<PERSON><PERSON>(name: 'ghi_chu')
  final String? ghiChu;
  @<PERSON>son<PERSON>ey(name: 'phan_hoi')
  final String? phanHoi;
  @JsonKey(name: 'app_xinnghi_status_id')
  final int? appXinnghiStatusId;

  const AbsentRequest({
    this.id,
    this.tsHocsinhId,
    this.appXinnghiTypeId,
    this.ngayNghiFrom,
    this.ngayNghiTo,
    this.tietNghiFrom,
    this.tietNghiTo,
    this.buoiNghiFrom,
    this.buoiNghiTo,
    this.lyDoNghi,
    this.phanHoi,
    this.ghiChu,
    this.appXinnghiStatusId,
  });

  @override
  String toString() {
    return 'AbsentRequest(id: $id, tsHocsinhId: $tsHocsinhId, appXinnghiTypeId: $appXinnghiTypeId, ngayNghiFrom: $ngayNghiFrom, ngayNghiTo: $ngayNghiTo, tietNghiFrom: $tietNghiFrom, tietNghiTo: $tietNghiTo, buoiNghiFrom: $buoiNghiFrom, buoiNghiTo: $buoiNghiTo, lyDoNghi: $lyDoNghi, phanHoi: $phanHoi, appXinnghiStatusId: $appXinnghiStatusId)';
  }

  factory AbsentRequest.fromJson(Map<String, dynamic> json) {
    return _$AbsentRequestFromJson(json);
  }

  Map<String, dynamic> toJson() => _$AbsentRequestToJson(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! AbsentRequest) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode =>
      id.hashCode ^
      tsHocsinhId.hashCode ^
      appXinnghiTypeId.hashCode ^
      ngayNghiFrom.hashCode ^
      ngayNghiTo.hashCode ^
      tietNghiFrom.hashCode ^
      tietNghiTo.hashCode ^
      buoiNghiFrom.hashCode ^
      buoiNghiTo.hashCode ^
      lyDoNghi.hashCode ^
      phanHoi.hashCode ^
      ghiChu.hashCode ^
      appXinnghiStatusId.hashCode;
}
