import 'package:collection/collection.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:myapp/data/model/config_response/hotline_response/hotline_person.dart';
import 'package:myapp/data/model/config_response/hotline_response/hotline_response.dart';
import 'package:myapp/data/model/parent_response/person_model.dart';
import 'dart:io' show Platform;
import 'img_splash.dart';
import 'app_valid_version.dart';

part 'config_response.g.dart';

@JsonSerializable()
class ConfigResponse {
  @<PERSON><PERSON><PERSON><PERSON>(name: 'IMG_SPLASH')
  final List<ImgSplash>? imgSplash;
  @J<PERSON><PERSON><PERSON>(name: 'IOS_VALID_VERSION')
  final List<AppValidVersion>? iosValidVersion;
  @J<PERSON><PERSON><PERSON>(name: 'ANDROID_VALID_VERSION')
  final List<AppValidVersion>? androidValidVersion;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'CONTACT_AND_HOTLINE')
  final List<HotlineResponse>? hotlines;
  @Json<PERSON>ey(name: 'HOTLINE')
  final List<HotlinePerson>? hotlines2;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'USER_GUIDE_LINK')
  final ImgSplash? userGuideLinks;

  final bool? isUnderMaintenance;

  const ConfigResponse({
    this.imgSplash,
    this.iosValidVersion,
    this.hotlines,
    this.hotlines2,
    this.androidValidVersion,
    this.isUnderMaintenance,
    this.userGuideLinks,
  });

  List<AppValidVersion>? get validVersions {
    return Platform.isIOS ? iosValidVersion : androidValidVersion;
  }

  List<PersonModel> get contact {
    return hotlines2
            ?.map((e) => PersonModel(
                  fullName: e.title,
                  fullNameEn: e.titleEn,
                  className: e.title,
                  classNameEn: e.titleEn,
                  email: e.email,
                  phoneNumber: e.phoneNumber,
                  extensionDescription: e.ext,
                ))
            .toList() ??
        [];

    //       List<PersonModel> get contact {
    // return hotlines
    //         ?.expand((element) => (element.titles ?? []).map((e) => PersonModel(
    //               fullName: e.title,
    //               className: e.title,
    //               classNameEn: e.titleEn,
    //               email: e.email,
    //               phoneNumber: e.phoneNumber,
    //             )))
    //         .toList() ??
    //     [];

    // return hotlines
    //         ?.firstWhereOrNull((e) => e.division == 'Principal office')
    //         ?.titles
    //         ?.map((e) => PersonModel(
    //               fullName: e.title,
    //               className: e.title,
    //               classNameEn: e.titleEn,
    //               email: e.email,
    //               phoneNumber: e.phoneNumber,
    //             ))
    //         .toList() ??
    //     [];
  }

  @override
  String toString() {
    return 'ConfigResponse(imgSplash: $imgSplash, iosValidVersion: $iosValidVersion)';
  }

  factory ConfigResponse.fromJson(Map<String, dynamic> json) {
    return _$ConfigResponseFromJson(json);
  }

  Map<String, dynamic> toJson() => _$ConfigResponseToJson(this);

  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    if (other is! ConfigResponse) return false;
    final mapEquals = const DeepCollectionEquality().equals;
    return mapEquals(other.toJson(), toJson());
  }

  @override
  int get hashCode => imgSplash.hashCode ^ iosValidVersion.hashCode ^ androidValidVersion.hashCode;
}
