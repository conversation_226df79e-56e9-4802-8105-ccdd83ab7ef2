import 'package:dartz/dartz.dart';
import 'package:com_common/core/base_response.dart';
import 'package:myapp/data/model/online_transaction_request/online_transaction_request.dart';
import 'package:myapp/data/model/request/uniform_confirm_tranfer_request/uniform_confirm_tranfer_request.dart';
import 'package:myapp/data/model/tution_fee_response/tuition_fee_response.dart';
import 'package:myapp/data/service/payment_service.dart';

abstract class PaymentRemoteDataSource {
  Future<Either<Exception, BaseResponse<List<TuitionFeeResponse>>>>
      getTuitionFee(
    int studentId,
    String year,
  );

  Future<Either<Exception, BaseResponse<dynamic>>> confirmOnlineTransaction(
      OnlineTransactionRequest request);

  Future<Either<Exception, BaseResponse<String>>> getFeeQrCode(
      int studentId, int orderId);

  Future<Either<Exception, BaseResponse<dynamic>>> confirmTransfer(
      UniformConfirmTranferRequest request);
}

class PaymentRemoteDataSourceImpl extends PaymentRemoteDataSource {
  PaymentService service;

  PaymentRemoteDataSourceImpl({required this.service});

  @override
  Future<Either<Exception, BaseResponse>> confirmTransfer(
      UniformConfirmTranferRequest request) async {
    try {
      final response = await service.transferConfirm(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<String>>> getFeeQrCode(
      int studentId, int orderId) async {
    try {
      final response = await service.getQrCode(studentId, orderId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> confirmOnlineTransaction(
      OnlineTransactionRequest request) async {
    try {
      final response = await service.onlineTransactionConfirm(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<TuitionFeeResponse>>>>
      getTuitionFee(int studentId, String year) async {
    try {
      final response = await service.getTuitionFee(
        studentId,
        year,
      );
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }
}
