import 'package:dartz/dartz.dart';
import 'package:myapp/data/model/notification_response/notification_response.dart';
import 'package:com_common/core/base_response.dart';
import 'package:myapp/data/model/notification_response/notification_type_response/notification_type_response.dart';
import 'package:myapp/data/service/notification_service.dart';

abstract class NotificationRemoteDataSource {
  Future<Either<Exception, BaseResponse<List<NotificationTypeResponse>>>>
      getNotificationType();

  Future<Either<Exception, BaseResponse<List<NotificationResponse>>>>
      getNotificationList(
          {String? searchKey,
          int? fromTime,
          int? toTime,
          List<int>? types,
          required int studentId,
          bool? isUnredOnly,
          required int pageNumber,
          required int pageSize});

  Future<Either<Exception, BaseResponse<dynamic>>> getUnreadCount(
      int studentId);

  Future<Either<Exception, BaseResponse<dynamic>>> markReaded(
      int notificationId);
  Future<Either<Exception, BaseResponse<dynamic>>> markAllReaded(int studentId);
}

class NotificationRemoteDataSourceImpl extends NotificationRemoteDataSource {
  NotificationService service;

  NotificationRemoteDataSourceImpl({required this.service});

  @override
  Future<Either<Exception, BaseResponse<List<NotificationResponse>>>>
      getNotificationList(
          {String? searchKey,
          int? fromTime,
          int? toTime,
          List<int>? types,
          required int studentId,
          bool? isUnredOnly,
          required int pageNumber,
          required int pageSize}) async {
    try {
      final response = await service.getNotificationList(
          studentId: studentId,
          searchKey: searchKey,
          fromTime: fromTime,
          toTime: toTime,
          types: types,
          isUnredOnly: isUnredOnly,
          pageNumber: pageNumber,
          pageSize: pageSize);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> getUnreadCount(int studentId) async {
    try {
      final response = await service.getUnreadCount(studentId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> markAllReaded(int studentId) async {
    try {
      final response = await service.markAllReaded(studentId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> markReaded(int notificationId) async {
    try {
      final response = await service.markReaded(notificationId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<NotificationTypeResponse>>>>
      getNotificationType() async {
    try {
      final response = await service.getNotificationType();
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }
}
