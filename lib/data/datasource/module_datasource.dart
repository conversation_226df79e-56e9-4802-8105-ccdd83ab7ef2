import 'package:dartz/dartz.dart';
import 'package:myapp/data/model/absent_reason_response/absent_reason_response.dart';
import 'package:myapp/data/model/absent_request/absent_request.dart';
import 'package:myapp/data/model/absent_response/absent_response.dart';
import 'package:myapp/data/model/atendance_response/atendance_response.dart';
import 'package:myapp/data/model/civilized_response/civilized_response.dart';
import 'package:myapp/data/model/config_response/nam_hoc.dart';
import 'package:myapp/data/model/exercise_response/exercise_response.dart';
import 'package:myapp/data/model/improvement_report_response/improvement_report_response.dart';
import 'package:myapp/data/model/meal_menu_response/meal_menu_response.dart';
import 'package:myapp/data/model/menu_group_response/menu_group_response.dart';
import 'package:myapp/data/model/monthly_report_response/monthly_report_response.dart';
import 'package:myapp/data/model/new_feed_response/new_feed_response.dart';
import 'package:myapp/data/model/register_payment_method_request/register_payment_method_request.dart';
import 'package:myapp/data/model/register_service_request/register_service_request.dart';
import 'package:myapp/data/model/register_subject_group_response/register_subject_group_response.dart';
import 'package:myapp/data/model/register_subject_request/register_subject_request.dart';
import 'package:myapp/data/model/register_subject_time_response/register_subject_time_response.dart';
import 'package:myapp/data/model/registered_payment_method/registered_payment_method.dart';
import 'package:myapp/data/model/registered_service_response/registered_service_response.dart';
import 'package:myapp/data/model/schedule_response/schedule_response.dart';
import 'package:myapp/data/model/score_group_response/score_group_list_response.dart';
import 'package:myapp/data/model/score_group_response/subject_response.dart';
import 'package:myapp/data/model/service_config_response/service_config_response.dart';
import 'package:myapp/data/model/submit_survey_request/submit_survey_request.dart';
import 'package:myapp/data/model/survey_detail_response/survey_detail_response.dart';
import 'package:myapp/data/model/survey_response/survey_response.dart';
import 'package:myapp/data/model/teacher_comment_feed_back_request/teacher_comment_feed_back_request.dart';
import 'package:myapp/data/model/teacher_comment_react_request/teacher_comment_react_request.dart';
import 'package:myapp/data/model/teacher_comment_response/teacher_comment_feedback.dart';
import 'package:myapp/data/model/teacher_comment_response/teacher_comment_response.dart';
import 'package:myapp/data/service/module_service.dart';
import 'package:com_common/core/base_response.dart';

abstract class ModuleRemoteDataSource {
  Future<Either<Exception, BaseResponse<List<MenuGroupResponse>>>> getModules(
      int studentId);

  Future<Either<Exception, BaseResponse<List<ScheduleResponse>>>> getSchedule(
      int studentId, int date);

  Future<Either<Exception, BaseResponse<List<AbsentResponse>>>>
      getAbsentRequestList(
          int studentId, String year, int pageNumber, int pageSize);

  Future<Either<Exception, BaseResponse<dynamic>>> submitRequest(
      AbsentRequest request);

  Future<Either<Exception, BaseResponse<dynamic>>> editRequest(
      AbsentRequest request);

  Future<Either<Exception, BaseResponse<dynamic>>> deleteRequest(int id);

  Future<Either<Exception, BaseResponse<AbsentResponse>>> absentRequestDetail(
      int id);

  Future<Either<Exception, BaseResponse<List<AbsentReasonResponse>>>>
      getAbsentReasonList();

  Future<Either<Exception, BaseResponse<List<AtendanceResponse>>>>
      getAtendanceList(int studentId, int date);

  Future<Either<Exception, BaseResponse<List<TeacherCommentResponse>>>>
      getTeacherCommentList(
          int studentId, String year, int date, int pageNumber, int pageSize);

  Future<Either<Exception, BaseResponse<dynamic>>> reactTeacherComment(
      TeacherCommentReactRequest request);

  Future<Either<Exception, BaseResponse<List<TeacherCommentFeedBack>>>>
      getTeacherCommentFeedBackList(
          int teacherCommentId, int studentId, int pageNumber, int pageSize);

  Future<Either<Exception, BaseResponse<dynamic>>> addComment(
      TeacherCommentFeedBackRequest request);

  Future<Either<Exception, BaseResponse<dynamic>>> deleteComment(int id);

  Future<Either<Exception, BaseResponse<dynamic>>> editComment(
      TeacherCommentFeedBackRequest request);

  Future<Either<Exception, BaseResponse<List<ExerciseResponse>>>>
      getExerciseList(
          String? keyword, int studentId, int pageNumber, int pageSize);

  Future<Either<Exception, BaseResponse<List<SubjectResponse>>>> getSubjectList(
      int studentId, String year, int semester);

  Future<Either<Exception, BaseResponse<ScoreGroupListResponse>>>
      getScoreBySubject(int studentId, String year, int semester, subjectId);

  Future<Either<Exception, BaseResponse<List<NamHoc>>>> getYearList();

  Future<Either<Exception, BaseResponse<CivilizedResponse>>> getCivilized(
      int studentId, String year);

  Future<Either<Exception, BaseResponse<List<ImprovementReportResponse>>>>
      getImprovementReport(int studentId, String year);

  Future<Either<Exception, BaseResponse<List<MonthlyReportResponse>>>>
      getMonthlyReport(int studentId, String year);

  Future<Either<Exception, BaseResponse<List<ServiceConfigResponse>>>>
      getConfigService(int cosoId, String year);

  Future<Either<Exception, BaseResponse<List<RegisteredServiceResponse>>>>
      getRegisteredServices(int studentId, int cosoId, String year);

  Future<Either<Exception, BaseResponse<List<RegisteredPaymentMethod>>>>
      getRegisteredPaymentMethod(int studentId, String year);

  Future<Either<Exception, BaseResponse>> submitRegisterService(
      RegisterServiceRequest request);

  Future<Either<Exception, BaseResponse>> submitPaymentMethod(
      RegisterPaymentMethodRequest request);

  Future<Either<Exception, BaseResponse<List<MealMenuResponse>>>> getMealMenu(
      int schoolId, int dateTime);

  Future<Either<Exception, BaseResponse<List<RegisterSubjectTimeResponse>>>>
      getRegisterSubjectTime(int studentId);

  Future<Either<Exception, BaseResponse<List<RegisterSubjectGroupResponse>>>>
      getRegisterSubjectGroup(int studentid, int timeId);

  Future<Either<Exception, BaseResponse>> submitRegisterSubject(
      RegisterSubjectRequest request);

  Future<Either<Exception, BaseResponse<List<SurveyResponse>>>> getSurveyList(
      int studentId);
  Future<Either<Exception, BaseResponse<SurveyDetailResponse>>> getSurveyDetail(
      int studentId, int surveyId);

  Future<Either<Exception, BaseResponse<dynamic>>> submitSurveyRequest(
      int studentId, int surveyId, SubmitSurveyRequest request);

  Future<Either<Exception, BaseResponse<List<NewFeedResponse>>>> getNewCalendar(
      int studentId, int date);

  Future<Either<Exception, BaseResponse>> confirmImprovementReport(int id);
}

class ModuleRemoteDataSourceImpl extends ModuleRemoteDataSource {
  ModuleService service;

  ModuleRemoteDataSourceImpl({required this.service});

  @override
  Future<Either<Exception, BaseResponse<List<MenuGroupResponse>>>> getModules(
      studentId) async {
    try {
      final response = await service.getModule(studentId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<ScheduleResponse>>>> getSchedule(
      studentId, date) async {
    try {
      final response = await service.getSchedule(studentId, date);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<AbsentResponse>>>>
      getAbsentRequestList(
          studentId, String year, int pageNumber, int pageSize) async {
    try {
      final response =
          await service.getAbsentList(studentId, year, pageNumber, pageSize);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> submitRequest(request) async {
    try {
      final response = await service.submitRequest(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> deleteRequest(int id) async {
    try {
      final response = await service.deleteRequest(id);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> editRequest(
      AbsentRequest request) async {
    try {
      final response = await service.editRequest(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<AbsentReasonResponse>>>>
      getAbsentReasonList() async {
    try {
      final response = await service.absentReasonList();
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<AtendanceResponse>>>>
      getAtendanceList(int studentId, int date) async {
    try {
      final response = await service.atendanceList(studentId, date);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<TeacherCommentResponse>>>>
      getTeacherCommentList(int studentId, String year, int date,
          int pageNumber, int pageSize) async {
    try {
      final response = await service.commentList(
          studentId, year, date, pageNumber, pageSize);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<AbsentResponse>>> absentRequestDetail(
      int id) async {
    try {
      final response = await service.getAbsentRequestDetail(id);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> addComment(
      TeacherCommentFeedBackRequest request) async {
    try {
      final response = await service.addComment(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> deleteComment(int id) async {
    try {
      final response = await service.deleteComment(id);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> editComment(
      TeacherCommentFeedBackRequest request) async {
    try {
      final response = await service.editComment(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<TeacherCommentFeedBack>>>>
      getTeacherCommentFeedBackList(int teacherCommentId, int studentId,
          int pageNumber, int pageSize) async {
    try {
      final response = await service.getTeacherCommentFeedBackList(
          teacherCommentId, studentId, pageNumber, pageSize);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> reactTeacherComment(
      TeacherCommentReactRequest request) async {
    try {
      final response = await service.reactTeacherComment(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<ExerciseResponse>>>>
      getExerciseList(
          String? keyword, int studentId, int pageNumber, int pageSize) async {
    try {
      final response = await service.getExerciseList(
          keyword, studentId, pageNumber, pageSize);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<ScoreGroupListResponse>>>
      getScoreBySubject(
          int studentId, String year, int semester, subjectId) async {
    try {
      final response =
          await service.getScoreBySubject(studentId, year, semester, subjectId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<SubjectResponse>>>> getSubjectList(
      int studentId, String year, int semester) async {
    try {
      final response = await service.getSubjectList(studentId, year, semester);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<NamHoc>>>> getYearList() async {
    try {
      final response = await service.getYearList();
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<CivilizedResponse>>> getCivilized(
      int studentId, String year) async {
    try {
      final response = await service.getCivilized(studentId, year);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<ImprovementReportResponse>>>>
      getImprovementReport(int studentId, String year) async {
    try {
      final response = await service.getImprovementReport(studentId, year);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<MonthlyReportResponse>>>>
      getMonthlyReport(int studentId, String year) async {
    try {
      final response = await service.getMonthlyReport(studentId, year);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<ServiceConfigResponse>>>>
      getConfigService(int cosoId, String year) async {
    try {
      final response = await service.getServiceConfig(cosoId, year);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<RegisteredPaymentMethod>>>>
      getRegisteredPaymentMethod(int studentId, String year) async {
    try {
      final response =
          await service.getRegisteredPaymentMethod(studentId, year);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<RegisteredServiceResponse>>>>
      getRegisteredServices(int studentId, int cosoId, String year) async {
    try {
      final response =
          await service.getRegisteredServices(studentId, cosoId, year);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> submitPaymentMethod(
      RegisterPaymentMethodRequest request) async {
    try {
      final response = await service.submitPaymentMethod(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> submitRegisterService(
      RegisterServiceRequest request) async {
    try {
      final response = await service.submitRegisterService(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<MealMenuResponse>>>> getMealMenu(
      int schoolId, int dateTime) async {
    try {
      final response = await service.getMealMenu(schoolId, dateTime);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<RegisterSubjectGroupResponse>>>>
      getRegisterSubjectGroup(int studentid, int timeId) async {
    try {
      final response = await service.getRegisterSubjectGroup(studentid, timeId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<RegisterSubjectTimeResponse>>>>
      getRegisterSubjectTime(int studentId) async {
    try {
      final response = await service.getRegisterTime(studentId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> submitRegisterSubject(
      RegisterSubjectRequest request) async {
    try {
      final response = await service.submitRegisterSubjectGroup(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<SurveyDetailResponse>>> getSurveyDetail(
      int studentId, int surveyId) async {
    try {
      final response = await service.getSurveyDetail(studentId, surveyId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<SurveyResponse>>>> getSurveyList(
      int studentId) async {
    try {
      final response = await service.getSurveyList(studentId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> submitSurveyRequest(
      int studentId, int surveyId, SubmitSurveyRequest request) async {
    try {
      final response = await service.submitSurvey(studentId, surveyId, request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<NewFeedResponse>>>> getNewCalendar(
      int studentId, int date) async {
    try {
      final response = await service.getNewCalendar(studentId, date);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> confirmImprovementReport(
      int id) async {
    try {
      final response = await service.confirmImprovementReport(id);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }
}
