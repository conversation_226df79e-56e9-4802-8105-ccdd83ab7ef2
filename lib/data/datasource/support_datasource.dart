import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:myapp/data/model/create_support_request/create_support_request.dart';
import 'package:myapp/data/model/support_reason_response/support_reason_response.dart';
import 'package:myapp/data/model/support_response/support_response.dart';
import 'package:com_common/core/base_response.dart';
import 'package:myapp/data/service/support_service.dart';

abstract class SupportRemoteDataSource {
  Future<Either<Exception, BaseResponse<List<SupportReasonResponse>>>>
      getSupportReasonList();

  Future<Either<Exception, BaseResponse<List<SupportResponse>>>>
      getSupportList();

  Future<Either<Exception, BaseResponse<SupportResponse>>> getSupportDetail(
    int id,
  );

  Future<Either<Exception, BaseResponse<dynamic>>> requestSupport({
    required int studentId,
    required int supportGroupId,
    required String reason,
    List<File>? images,
  });

  Future<Either<Exception, BaseResponse<dynamic>>> editRequestSupport(
      CreateSupportRequest request);

  Future<Either<Exception, BaseResponse<dynamic>>> deleteRequestSupport(int id);
}

class SupportRemoteDataSourceImpl extends SupportRemoteDataSource {
  SupportService service;

  SupportRemoteDataSourceImpl({required this.service});

  @override
  Future<Either<Exception, BaseResponse<dynamic>>> deleteRequestSupport(
      int id) async {
    try {
      final response = await service.delete(id);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> editRequestSupport(
      CreateSupportRequest request) async {
    try {
      final response = await service.editSupportRequest(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<SupportResponse>>> getSupportDetail(
      int id) async {
    try {
      final response = await service.getDetail(id);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<SupportResponse>>>>
      getSupportList() async {
    try {
      final response = await service.getSupportList();
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<SupportReasonResponse>>>>
      getSupportReasonList() async {
    try {
      final response = await service.getSupportReasonList();
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> requestSupport(
      {required int studentId,
      required int supportGroupId,
      required String reason,
      List<File>? images}) async {
    try {
      final response = await service.requestSupport(
          groupId: supportGroupId,
          studentId: studentId,
          reason: reason,
          images: images);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }
}
