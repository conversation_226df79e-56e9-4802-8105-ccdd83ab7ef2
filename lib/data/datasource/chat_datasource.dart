import 'package:dartz/dartz.dart';
import 'package:com_common/core/base_response.dart';
import 'package:myapp/data/model/send_notification_request/send_notification_request.dart';
import 'package:myapp/data/service/chat_service.dart';

abstract class ChatRemoteDataSource {
  Future<Either<Exception, BaseResponse>> addMember(
      SendNotificationRequest request);

  Future<Either<Exception, BaseResponse>> sendNotification(
      SendNotificationRequest request);
}

class ChatRemoteDataSourceImpl extends ChatRemoteDataSource {
  ChatService service;

  ChatRemoteDataSourceImpl({required this.service});

  @override
  Future<Either<Exception, BaseResponse>> addMember(
      SendNotificationRequest request) async {
    try {
      final response = await service.addMember(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> sendNotification(
      SendNotificationRequest request) async {
    try {
      final response = await service.sendNotification(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }
}
