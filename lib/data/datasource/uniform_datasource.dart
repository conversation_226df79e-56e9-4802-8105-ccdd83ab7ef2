import 'package:dartz/dartz.dart';
import 'package:com_common/core/base_response.dart';
import 'package:myapp/data/model/request/uniform_add_set_product_request/uniform_add_set_product_request.dart';
import 'package:myapp/data/model/request/uniform_cart_change_size/uniform_cart_change_size_request.dart';
import 'package:myapp/data/model/request/uniform_cart_request/uniform_cart_request.dart';
import 'package:myapp/data/model/request/uniform_confirm_tranfer_request/uniform_confirm_tranfer_request.dart';
import 'package:myapp/data/model/request/uniform_submit_request/uniform_submit_request.dart';
import 'package:myapp/data/model/response/uniform_cart_response/uniform_set_cart_detail_response.dart';
import 'package:myapp/data/model/response/uniform_category_response/uniform_category_response.dart';
import 'package:myapp/data/model/response/uniform_oder_response/uniform_oder_response.dart';
import 'package:myapp/data/model/response/uniform_product_response/uniform_product_response.dart';
import 'package:myapp/data/model/response/uniform_set_response/uniform_set_response.dart';
import 'package:myapp/data/service/uniform_service.dart';

abstract class UniformRemoteDataSource {
  Future<Either<Exception, BaseResponse<UniformSetCartDetailResponse>>> getCart(
      int studentId);

  Future<Either<Exception, BaseResponse<dynamic>>> addCart(
      UniformCartRequest request);

  Future<Either<Exception, BaseResponse<dynamic>>> removeFromCart(
      UniformCartRequest request);

  Future<Either<Exception, BaseResponse<dynamic>>> changeSize(
      UniformCartChangeSizeRequest request);

  Future<Either<Exception, BaseResponse<List<UniformCategoryResponse>>>>
      getCategory(int studentId);

  Future<Either<Exception, BaseResponse<List<UniformProductResponse>>>>
      getProduct(int studentId, int categoryId);

  Future<Either<Exception, BaseResponse<List<UniformOderResponse>>>>
      getOrderList(int studentId);

  Future<Either<Exception, BaseResponse<UniformProductResponse>>> productDetail(
      int studentId, int productId);

  Future<Either<Exception, BaseResponse<UniformOderResponse>>> submit(
      UniformSubmitRequest request);

  Future<Either<Exception, BaseResponse<dynamic>>> confirmTransfer(
      UniformConfirmTranferRequest request);

  Future<Either<Exception, BaseResponse<dynamic>>> confirmOnlineTransaction(
      UniformConfirmTranferRequest request);

  Future<Either<Exception, BaseResponse<List<UniformSetResponse>>>>
      getSetComboList(int studentId, {bool isPaid = false});

  Future<Either<Exception, BaseResponse<UniformSetResponse>>>
      getSetComboListDetail(int studentId, int setId);

  Future<Either<Exception, BaseResponse<dynamic>>> addSetProductToCart(
      UniformAddSetProductRequest request);

  Future<Either<Exception, BaseResponse<dynamic>>> deleteOrder(
      int orderId, String? reason);

  Future<Either<Exception, BaseResponse<String>>> getQrCode(
      int studentId, int orderId);
}

class UniformRemoteDataSourceImpl extends UniformRemoteDataSource {
  UniformService service;

  UniformRemoteDataSourceImpl({required this.service});

  @override
  Future<Either<Exception, BaseResponse>> addCart(
      UniformCartRequest request) async {
    try {
      final response = await service.addToCart(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<UniformSetCartDetailResponse>>> getCart(
      int studentId) async {
    try {
      final response = await service.getCart(studentId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<UniformCategoryResponse>>>>
      getCategory(int studentId) async {
    try {
      final response = await service.getCategoryList(studentId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<UniformProductResponse>>>>
      getProduct(int studentId, int categoryId) async {
    try {
      final response = await service.getProductList(studentId, categoryId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<UniformProductResponse>>> productDetail(
      int studentId, int productId) async {
    try {
      final response = await service.getProductDetail(studentId, productId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> changeSize(
      UniformCartChangeSizeRequest request) async {
    try {
      final response = await service.changeSize(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> removeFromCart(
      UniformCartRequest request) async {
    try {
      final response = await service.removeFromCart(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> confirmTransfer(
      UniformConfirmTranferRequest request) async {
    try {
      final response = await service.transferConfirm(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<UniformOderResponse>>> submit(
      UniformSubmitRequest request) async {
    try {
      final response = await service.submit(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<UniformOderResponse>>>>
      getOrderList(
    int studentId,
  ) async {
    try {
      final response = await service.getOrderList(studentId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> confirmOnlineTransaction(
      UniformConfirmTranferRequest request) async {
    try {
      final response = await service.onlineTransactionConfirm(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<UniformSetResponse>>>>
      getSetComboList(int studentId, {bool isPaid = false}) async {
    try {
      final response = await (isPaid
          ? service.getPairProductSet(studentId)
          : service.getProductSet(studentId));
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<UniformSetResponse>>>
      getSetComboListDetail(int studentId, int setId) async {
    try {
      final response = await service.getProductSetDetail(studentId, setId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> addSetProductToCart(
      UniformAddSetProductRequest request) async {
    try {
      final response = await service.addSetProductToCart(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<dynamic>>> deleteOrder(
      int orderId, String? reason) async {
    try {
      final response = await service.deleteOrder(orderId, reason);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<String>>> getQrCode(
      int studentId, int orderId) async {
    try {
      final response = await service.getQrCode(studentId, orderId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }
}
