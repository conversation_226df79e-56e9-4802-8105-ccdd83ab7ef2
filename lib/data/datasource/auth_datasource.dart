import 'package:dartz/dartz.dart';
import 'package:myapp/data/model/config_response/config_response.dart';
import 'package:myapp/data/model/sign_in_response/sign_in_response.dart';
import 'package:myapp/data/model/sign_in_response/token_info.dart';
import 'package:myapp/data/model/sign_out_request/sign_out_request.dart';
import 'package:myapp/data/model/signin/signin_request.dart';
import 'package:myapp/data/service/auth_service.dart';
import 'package:com_common/core/base_response.dart';

abstract class AuthRemoteDataSource {
  Future<Either<Exception, BaseResponse<SignInResponse>>> signin(
      SignInRequest request);
  Future<Either<Exception, BaseResponse>> signOut(SignOutRequest request);

  Future<Either<Exception, BaseResponse<dynamic>>> verifyToken(
      TokenInfo request);

  Future<Either<Exception, BaseResponse<ConfigResponse>>> getConfig();
}

class AuthRemoteDataSourceImpl extends AuthRemoteDataSource {
  AuthService service;

  AuthRemoteDataSourceImpl({required this.service});

  @override
  Future<Either<Exception, BaseResponse<SignInResponse>>> signin(
      SignInRequest request) async {
    try {
      final response = await service.signin(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> verifyToken(TokenInfo request) async {
    try {
      final response = await service.verifyToken(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<ConfigResponse>>> getConfig() async {
    try {
      final response = await service.getConfig();
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> signOut(
      SignOutRequest request) async {
    try {
      final response = await service.signOut(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }
}
