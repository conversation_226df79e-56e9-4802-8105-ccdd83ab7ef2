import 'package:dartz/dartz.dart';
import 'package:myapp/data/model/article/article_bookmark_request/article_bookmark_request.dart';
import 'package:myapp/data/model/article/article_reaction_request/article_reaction_request.dart';
import 'package:myapp/data/model/article/article_response/article_response.dart';
import 'package:myapp/data/model/article/comment_reaction_request/comment_reaction_request.dart';
import 'package:myapp/data/model/article/comment_request/comment_request.dart';
import 'package:myapp/data/model/article/comment_response/comment_response.dart';

import 'package:myapp/data/model/article/edit_comment_request/edit_comment_request.dart';
import 'package:myapp/data/model/article_category_response/article_category_response.dart';
import 'package:myapp/data/service/article_service.dart';
import 'package:com_common/core/base_response.dart';

abstract class ArticleRemoteDataSource {
  Future<Either<Exception, BaseResponse<List<ArticleResponse>>>> getArticleList(
      {String? searchKey,
      int? categoryId,
      required int studentId,
      required int pageNumber,
      required int pageSize});

  Future<Either<Exception, BaseResponse<List<ArticleResponse>>>>
      getPublicArticleList(
          {String? searchKey,
          required int groupId,
          required int pageNumber,
          required int pageSize});

  Future<Either<Exception, BaseResponse<List<ArticleCategoryResponse>>>>
      getCategoryList(
    int studentId,
  );

  Future<Either<Exception, BaseResponse<ArticleResponse>>> getArticleDetail({
    required int articleId,
    int? studentId,
  });

  Future<Either<Exception, BaseResponse<dynamic>>> reactArtile(
      ArticleReactionRequest request);

  Future<Either<Exception, BaseResponse<dynamic>>> reactArtileComment(
      CommentReactionRequest request);

  Future<Either<Exception, BaseResponse<List<CommentResponse>>>> getCommentList(
      int articleId, int studentId, int pageNumber, int pageSize);

  Future<Either<Exception, BaseResponse<List<CommentResponse>>>>
      getCommentReplyList(int articleId, int commentId, int studentId,
          int pageNumber, int pageSize);

  Future<Either<Exception, BaseResponse<dynamic>>> addComment(
      CommentRequest request);

  Future<Either<Exception, BaseResponse<dynamic>>> deleteComment(int comment);

  Future<Either<Exception, BaseResponse<dynamic>>> editComment(
      EditCommentRequest request);

  Future<Either<Exception, BaseResponse<List<ArticleResponse>>>>
      getBookmarkList(
          {String? searchKey,
          required int studentId,
          required int pageNumber,
          required int pageSize});

  Future<Either<Exception, BaseResponse<dynamic>>> bookmark(
      ArticleBookmarkRequest request);

  Future<Either<Exception, BaseResponse<dynamic>>> deleteBookmark(
      int articleId);
}

class ArticleRemoteDataSourceImpl extends ArticleRemoteDataSource {
  ArticleService service;

  ArticleRemoteDataSourceImpl({required this.service});

  @override
  Future<Either<Exception, BaseResponse>> addComment(
      CommentRequest request) async {
    try {
      final response = await service.addComment(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> deleteComment(int comment) async {
    try {
      final response = await service.deleteComment(comment);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> editComment(
      EditCommentRequest request) async {
    try {
      final response = await service.editComment(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<ArticleResponse>>> getArticleDetail({
    required int articleId,
    int? studentId,
  }) async {
    try {
      final response = await service.getArticleDetail(articleId, studentId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<ArticleResponse>>>> getArticleList(
      {String? searchKey,
      int? categoryId,
      required int studentId,
      required int pageNumber,
      required int pageSize}) async {
    try {
      final response = await service.getArticleList(
          searchKey: searchKey,
          categoryId: categoryId,
          studentId: studentId,
          pageNumber: pageNumber,
          pageSize: pageSize);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> reactArtile(
      ArticleReactionRequest request) async {
    try {
      final response = await service.reactArtile(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> reactArtileComment(
      CommentReactionRequest request) async {
    try {
      final response = await service.reactCommentArtile(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<CommentResponse>>>> getCommentList(
      int articleId, int studentId, int pageNumber, int pageSize) async {
    try {
      final response = await service.getArticleCommentList(
          articleId, studentId, pageNumber, pageSize);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<CommentResponse>>>>
      getCommentReplyList(int articleId, int commentId, int studentId,
          int pageNumber, int pageSize) async {
    try {
      final response = await service.getArticleCommentReplyList(
          articleId, studentId, commentId, pageNumber, pageSize);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> bookmark(
      ArticleBookmarkRequest request) async {
    try {
      final response = await service.bookmark(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> deleteBookmark(int articleId) async {
    try {
      final response = await service.deleteBookmark(articleId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<ArticleResponse>>>>
      getBookmarkList(
          {String? searchKey,
          required int studentId,
          required int pageNumber,
          required int pageSize}) async {
    try {
      final response = await service.getBookmarkList(
          searchKey: searchKey,
          studentId: studentId,
          pageNumber: pageNumber,
          pageSize: pageSize);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<ArticleCategoryResponse>>>>
      getCategoryList(int studentId) async {
    try {
      final response = await service.getCategoryList(studentId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<ArticleResponse>>>>
      getPublicArticleList(
          {String? searchKey,
          required int groupId,
          required int pageNumber,
          required int pageSize}) async {
    try {
      final response = await service.getPublicArticleList(
          searchKey: searchKey,
          groupId: groupId,
          pageNumber: pageNumber,
          pageSize: pageSize);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }
}
