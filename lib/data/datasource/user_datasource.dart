import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:myapp/data/model/change_password_request/change_password_request.dart';
import 'package:myapp/data/model/parent_response/person_response.dart';
import 'package:myapp/data/model/password_recover/pass_word_recover_request/pass_word_recover_request.dart';
import 'package:myapp/data/model/password_recover/verify_otp_request/verify_otp_request.dart';
import 'package:myapp/data/model/sign_in_response/sign_in_response.dart';
import 'package:myapp/data/model/sign_in_response/token_info.dart';
import 'package:myapp/data/model/teacher_list_response/teacher_list_response.dart';
import 'package:myapp/data/model/update_profile_request/update_profile_request.dart';
import 'package:myapp/data/service/user_service.dart';
import 'package:com_common/core/base_response.dart';

abstract class UserRemoteDataSource {
  Future<Either<Exception, BaseResponse<dynamic>>> getOtp(
      PassWordRecoverRequest request);

  Future<Either<Exception, BaseResponse<SignInResponse>>> getProfile();

  Future<Either<Exception, BaseResponse<dynamic>>> recover(
      VerifyOtpRequest request);

  Future<Either<Exception, BaseResponse<dynamic>>> changePassword(
      ChangePasswordRequest request);

  Future<Either<Exception, BaseResponse<dynamic>>> updateProfile(
      UpdateProfileRequest request);

  Future<Either<Exception, BaseResponse<List<PersonResponse>>>> getParentList(
    int studentId,
  );

  Future<Either<Exception, BaseResponse<TeacherListResponse>>> getTeacherList(
    int studentId,
  );

  Future<Either<Exception, BaseResponse<TokenInfo>>> refreshToken(
      TokenInfo request);

  Future<Either<Exception, BaseResponse>> uploadImage(File image);
}

class UserRemoteDataSourceImpl extends UserRemoteDataSource {
  UserService service;

  UserRemoteDataSourceImpl({required this.service});

  @override
  Future<Either<Exception, BaseResponse>> getOtp(request) async {
    try {
      final response = await service.getOtp(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<SignInResponse>>> getProfile() async {
    try {
      final response = await service.getProfile();
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> changePassword(
      ChangePasswordRequest request) async {
    try {
      final response = await service.changePassword(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> recover(
      VerifyOtpRequest request) async {
    try {
      final response = await service.recover(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> updateProfile(
      UpdateProfileRequest request) async {
    try {
      final response = await service.update(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<PersonResponse>>>> getParentList(
      int studentId) async {
    try {
      final response = await service.getParentList(studentId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<TeacherListResponse>>> getTeacherList(
      int studentId) async {
    try {
      final response = await service.getTeacherList(studentId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<TokenInfo>>> refreshToken(
      TokenInfo request) async {
    try {
      final response = await service.refreshToken(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> uploadImage(File image) async {
    try {
      final response = await service.uploadAvatar(image: image);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }
}
