import 'package:dartz/dartz.dart';
import 'package:myapp/data/model/bus_absence_request/bus_absence_request.dart';
import 'package:myapp/data/model/bus_absence_response/bus_absence_response.dart';
import 'package:myapp/data/model/bus_data_response/bus_data_response.dart';
import 'package:com_common/core/base_response.dart';
import 'package:myapp/data/model/bus_history_response/bus_history_response.dart';
import 'package:myapp/data/model/bus_service_response/bus_service_response.dart';
import 'package:myapp/data/model/bus_service_resquest/bus_service_request.dart';
import 'package:myapp/data/model/route_response/route_response.dart';
import 'package:myapp/data/service/bus_service.dart';

abstract class BusRemoteDataSource {
  Future<Either<Exception, BaseResponse<List<BusServiceResponse>>>>
      currentService(int studentId, String year);

  Future<Either<Exception, BaseResponse<List<dynamic>>>> listBusStop(
      int cosoId);

  Future<Either<Exception, BaseResponse<List<RouteResponse>>>> getRouteList(
      int busStopId);

  Future<Either<Exception, BaseResponse<BusDataResponse>>> config(
      int cosoId, String year);

  Future<Either<Exception, BaseResponse<dynamic>>> submit(
      BusServiceRequest payload);
  Future<Either<Exception, BaseResponse<dynamic>>> update(
      BusServiceRequest payload);
  Future<Either<Exception, BaseResponse<dynamic>>> cancel(int id);

  Future<Either<Exception, BaseResponse<List<BusHistoryResponse>>>> history(
      int studentId, int fromTime, int toTime, int pageNumber, int pageSize);

  Future<Either<Exception, BaseResponse<dynamic>>> submitBusAbsence(
      BusAbsenceRequest payload);
  Future<Either<Exception, BaseResponse<dynamic>>> updateBusAbsence(
      BusAbsenceRequest payload);
  Future<Either<Exception, BaseResponse<dynamic>>> cancelBusAbsence(int id);

  Future<Either<Exception, BaseResponse<List<BusAbsenceResponse>>>>
      getBusAbsence(int studentId, String year, int pageNumber, int pageSize);
}

class BusRemoteDataSourceImpl extends BusRemoteDataSource {
  BusService service;

  BusRemoteDataSourceImpl({required this.service});

  @override
  Future<Either<Exception, BaseResponse<BusDataResponse>>> config(
      int cosoId, String year) async {
    try {
      final response = await service.listConfig(cosoId, year);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<BusServiceResponse>>>>
      currentService(int studentId, String year) async {
    try {
      final response = await service.currentService(studentId, year);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<dynamic>>>> listBusStop(
      int cosoId) async {
    try {
      final response = await service.listBusStop(
        cosoId,
      );
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> submit(payload) async {
    try {
      final response = await service.submit(payload);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> update(payload) async {
    try {
      final response = await service.update(payload);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> cancel(int id) async {
    try {
      final response = await service.cancel(id);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<RouteResponse>>>> getRouteList(
      int busStopId) async {
    try {
      final response = await service.getRouteList(busStopId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<BusHistoryResponse>>>> history(
      int studentId,
      int fromTime,
      int toTime,
      int pageNumber,
      int pageSize) async {
    try {
      final response = await service.history(
          studentId, fromTime, toTime, pageNumber, pageSize);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> cancelBusAbsence(int id) async {
    try {
      final response = await service.cancelAbsenceRequest(id);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<BusAbsenceResponse>>>>
      getBusAbsence(
          int studentId, String year, int pageNumber, int pageSize) async {
    try {
      final response = await service.getBusAbsenceList(
          studentId, year, pageNumber, pageSize);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> submitBusAbsence(
      BusAbsenceRequest payload) async {
    try {
      final response = await service.submitAbsenceRequest(payload);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> updateBusAbsence(
      BusAbsenceRequest payload) async {
    try {
      final response = await service.updateAbsenceRequest(payload);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }
}
