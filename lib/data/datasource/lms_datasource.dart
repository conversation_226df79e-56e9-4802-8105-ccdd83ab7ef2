import 'package:dartz/dartz.dart';
import 'package:com_common/core/base_response.dart';
import 'package:myapp/data/model/assignment_response/assignment_response.dart';
import 'package:myapp/data/service/lms_service.dart';

abstract class LMSRemoteDataSource {
  Future<Either<Exception, BaseResponse<List<AssignmentResponse>>>>
      getAssignmentList(
          String? keyword, int studentId, int pageNumber, int pageSize);

  Future<Either<Exception, BaseResponse<dynamic>>> getAssignmentLink(
      int assignmentId, int studentId);
}

class LMSRemoteDataSourceImpl extends LMSRemoteDataSource {
  LMSService service;

  LMSRemoteDataSourceImpl({required this.service});

  @override
  Future<Either<Exception, BaseResponse<List<AssignmentResponse>>>>
      getAssignmentList(
          String? keyword, int studentId, int pageNumber, int pageSize) async {
    try {
      final response = await service.getAssignmentList(
          keyword, studentId, pageNumber, pageSize);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<dynamic>>> getAssignmentLink(
      int assignmentId, int studentId) async {
    try {
      final response = await service.getAssignmentLink(assignmentId, studentId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }
}
