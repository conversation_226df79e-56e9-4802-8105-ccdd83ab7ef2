import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:myapp/data/model/prescription/delete_image_request.dart';
import 'package:myapp/data/model/prescription/prescription_request.dart';
import 'package:myapp/data/model/prescription/prescription_response.dart';
import 'package:com_common/core/base_response.dart';
import 'package:myapp/data/service/prescription_service.dart';

abstract class PrescriptionRemoteDataSource {
  Future<Either<Exception, BaseResponse<List<PrescriptionResponse>>>>
      getPrescriptionList(
          {required int studentId,
          required int pageNumber,
          required int pageSize});

  Future<Either<Exception, BaseResponse<dynamic>>> uploadImage(
      {List<File>? images, required int id});

  Future<Either<Exception, BaseResponse<dynamic>>> create(
      PrescriptionRequest request);

  Future<Either<Exception, BaseResponse<dynamic>>> edit(
      PrescriptionRequest request);

  Future<Either<Exception, BaseResponse<dynamic>>> deleteImage(
      DeleteImageRequest request);

  Future<Either<Exception, BaseResponse<dynamic>>> delete(int id);
}

class PrescriptionRemoteDataSourceImpl extends PrescriptionRemoteDataSource {
  PrescriptionService service;

  PrescriptionRemoteDataSourceImpl({required this.service});

  @override
  Future<Either<Exception, BaseResponse<dynamic>>> create(
      PrescriptionRequest request) async {
    try {
      final response = await service.create(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> delete(int id) async {
    try {
      final response = await service.delete(id);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> edit(
      PrescriptionRequest request) async {
    try {
      final response = await service.edit(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<PrescriptionResponse>>>>
      getPrescriptionList(
          {required int studentId,
          required int pageNumber,
          required int pageSize}) async {
    try {
      final response =
          await service.getPrescriptionList(studentId, pageNumber, pageSize);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> uploadImage(
      {List<File>? images, required int id}) async {
    try {
      final response = await service.uploadImage(
        id,
        images: images,
      );
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> deleteImage(
      DeleteImageRequest request) async {
    try {
      final response = await service.deleteImages(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }
}
