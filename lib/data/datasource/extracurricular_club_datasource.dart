import 'package:dartz/dartz.dart';
import 'package:com_common/core/base_response.dart';
import 'package:myapp/data/model/extracurricular_club_request/extracurricular_club_request.dart';
import 'package:myapp/data/model/extracurricular_club_request/register_extracurricular_request/register_extracurricular_request.dart';
import 'package:myapp/data/model/extracurricular_club_response/club_order_response.dart';
import 'package:myapp/data/model/extracurricular_club_response/extracurricular_club_cart_response/extracurricular_club_cart_response.dart';
import 'package:myapp/data/model/extracurricular_club_response/extracurricular_club_response.dart';
import 'package:myapp/data/model/request/uniform_confirm_tranfer_request/uniform_confirm_tranfer_request.dart';
import 'package:myapp/data/service/extracurricular_club_service.dart';

abstract class ExtracurricularClubRemoteDataSource {
  Future<Either<Exception, BaseResponse<List<ExtracurricularClubResponse>>>>
      getPublishClubList(int studentId);

  Future<Either<Exception, BaseResponse<ExtracurricularClubCartResponse>>>
      getRegisteredClubList(int studentId);

  Future<Either<Exception, BaseResponse<ExtracurricularClubResponse>>>
      getClubDetail(int studentId, int clubId);

  Future<Either<Exception, BaseResponse>> addToCart(
      int studentId, int clubId, RegisterExtracurricularRequest request);

  Future<Either<Exception, BaseResponse>> removeFromCart(
      int studentId, int clubId);

  Future<Either<Exception, BaseResponse<ClubOrderResponse>>> submit(
      int studentId, ExtracurricularClubRequest request);

  Future<Either<Exception, BaseResponse<List<ClubOrderResponse>>>> getOrderList(
      int studentId);

  Future<Either<Exception, BaseResponse<ClubOrderResponse>>> getOrderDetail(
      int studentId, int orderId);

  Future<Either<Exception, BaseResponse<String>>> getClubQrCode(
      int studentId, int orderId);

  Future<Either<Exception, BaseResponse>> confirmTransfer(
      int studentId, int orderId);

  Future<Either<Exception, BaseResponse<dynamic>>> confirmOnlineTransaction(
      UniformConfirmTranferRequest request);
}

class ExtracurricularClubRemoteDataSourceImpl
    extends ExtracurricularClubRemoteDataSource {
  ExtracurricularClubService service;

  ExtracurricularClubRemoteDataSourceImpl({required this.service});

  @override
  Future<Either<Exception, BaseResponse>> confirmOnlineTransaction(
      UniformConfirmTranferRequest request) async {
    try {
      final response = await service.onlineTransactionConfirm(request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> addToCart(
      int studentId, int clubId, RegisterExtracurricularRequest request) async {
    try {
      final response = await service.addClubToCart(studentId, clubId, request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<ExtracurricularClubResponse>>>
      getClubDetail(int studentId, int clubId) async {
    try {
      final response = await service.getClubDetail(studentId, clubId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<ExtracurricularClubResponse>>>>
      getPublishClubList(int studentId) async {
    try {
      final response = await service.getPublishClubList(studentId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<ExtracurricularClubCartResponse>>>
      getRegisteredClubList(int studentId) async {
    try {
      final response = await service.getRegisteredClubList(studentId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> removeFromCart(
      int studentId, int clubId) async {
    try {
      final response = await service.removeClubFromCart(studentId, clubId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<ClubOrderResponse>>> submit(
      int studentId, ExtracurricularClubRequest request) async {
    try {
      final response = await service.submitClub(studentId, request);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse>> confirmTransfer(
      int studentId, int orderId) async {
    try {
      final response = await service.transferConfirm(studentId, orderId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<String>>> getClubQrCode(
      int studentId, int orderId) async {
    try {
      final response = await service.getQrCode(studentId, orderId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<ClubOrderResponse>>> getOrderDetail(
      int studentId, int orderId) async {
    try {
      final response = await service.getOrderDetail(studentId, orderId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }

  @override
  Future<Either<Exception, BaseResponse<List<ClubOrderResponse>>>> getOrderList(
      int studentId) async {
    try {
      final response = await service.getOrderList(studentId);
      return Right(response.data);
    } on Exception catch (error) {
      return Left(error);
    }
  }
}
