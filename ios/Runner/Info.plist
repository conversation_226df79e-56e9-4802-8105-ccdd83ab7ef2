<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>Edi-Parents</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>Edi-Parents</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>MinimumOSVersion</key>
		<string>13.0</string>
		<key>CFBundleShortVersionString</key>
		<string>$(MARKETING_VERSION)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(CURRENT_PROJECT_VERSION)</string>
		<key>FirebaseAppDelegateProxyEnabled</key>
		<false/>
		<key>ITSAppUsesNonExemptEncryption</key>
		<false/>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>sms</string>
			<string>tel</string>
			<string>mailto</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>Cho phép truy cập vị trí để chia sẻ vị trí với nhà trường</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>Cho phép truy cập vị trí để chia sẻ vị trí với nhà trường</string>
		<key>NSCameraUsageDescription</key>
		<string>Cho phép truy cập máy ảnh chụp và đính kèm để đăng bài viết, hỗ trợ, phản hồi giáo viên</string>
		<key>NSFaceIDUsageDescription</key>
		<string>Cho phép sử dụng sinh trắc học để đăng nhập?</string>
		<key>NSMicrophoneUsageDescription</key>
		<string>Cho phép truy cập Microphone của bạn để phát hoặc thu audio trong nội dung</string>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>Cho phép truy cập thư viện ảnh đính kèm để đăng bài viết, hỗ trợ, phản hồi giáo viên</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>Cho phép truy cập thư viện ảnh đính kèm để đăng bài viết, hỗ trợ, phản hồi giáo viên</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UIBackgroundModes</key>
		<array>
			<string>fetch</string>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<true/>
	</dict>
</plist>
