helpFunction()
{
   echo ""
   echo "Usage: $0 -e Environment"
   echo -e "\t-e Environment"
   echo -e "\t-v Server version"
   exit 1 # Exit script after printing help
}

while getopts "e:v:" opt
do
   case "$opt" in
      e ) ENV="$OPTARG" ;;
      v ) VERSION="$OPTARG" ;;
      ? ) helpFunction ;; # Print helpFunction in case parameter is non-existent
   esac
done

# Print helpFunction in case parameters are empty [dev, test, production]
if [ -z "$ENV" ]
then
   ENV="${ENV:-TEST}"

fi

if [ -z "$VERSION" ]
then
   VERSION="${VERSION:-v1.42}"
fi

# Begin script in case all parameters are correct
echo "BUILD APP with environment = $ENV and Back-end version = $VERSION"

echo "> 0. Go to IOS forder and increase buildnumber"
cd ios && agvtool next-version

echo "> 1. Get Curent build number"
getVersion() {
    retval=$(agvtool what-version)
}

retval=$( getVersion )
VERSION_NUMBER="$(cut -d':' -f2 <<<"$retval")"
CURRENT_PROJECT_VERSION="$(echo "${VERSION_NUMBER}" | tr -d '[:space:]')"
MARKETING_VERSION='2.1.8'

echo "> 2. Remove old build forder, rebuild Archive and upload to testflight"

# Create necessary directories to avoid PathNotFoundException
mkdir -p build/ios/ipa

if cd .. && rm -rf build && mkdir -p build/ios/ipa && flutter build ipa --release --dart-define=environment="$ENV"  --export-options-plist=ios/Runner/exportOptionsPlist.plist; then
   echo "> 3. Archive and upload"
   if git add . && git commit -m "bump ios version $MARKETING_VERSION ($CURRENT_PROJECT_VERSION)" && git push; then
      # PRE='https://api.telegram.org/bot6307644201:AAEz0HMx-HuaUfr3sb1KtuJ80QmU6HRj0rA/sendMessage?chat_id=-1001812118956&text=Gi%C3%A1o%20vi%C3%AAn%20c%C3%B3%20b%E1%BA%A3n%20c%E1%BA%ADp%20nh%E1%BA%ADt%20iOS%20m%E1%BB%9Bi%20m%C3%B4i%20tr%C6%B0%E1%BB%9Dng%20'
      # POST='%20%3A%0A%20https%3A%2F%2Ftestflight.apple.com%2Fjoin%2FDuVIE8f1'
      # SPACE='%20'
      # URL="$PRE$ENV$SPACE$MARKETING_VERSION$SPACE($CURRENT_PROJECT_VERSION)$POST"
      # curl --location  $URL
      echo "DONE"
   else
         echo "error"
         # curl --location 'https://api.telegram.org/bot6307644201:AAEz0HMx-HuaUfr3sb1KtuJ80QmU6HRj0rA/sendMessage?chat_id=-1001812118956&text=%C4%90%E1%BA%A9y%20Git%20l%E1%BB%97i%20r%E1%BB%93i%20th%E1%BA%A7y%20S%C6%A1n%20%C6%A1i!'
   fi
else
   echo "error"
   #  curl --location 'https://api.telegram.org/bot6307644201:AAEz0HMx-HuaUfr3sb1KtuJ80QmU6HRj0rA/sendMessage?chat_id=-1001812118956&text=Ios%20Build%20l%E1%BB%97i%20r%E1%BB%93i%20th%E1%BA%A7y%20S%C6%A1n%20%C6%A1i!'
fi
