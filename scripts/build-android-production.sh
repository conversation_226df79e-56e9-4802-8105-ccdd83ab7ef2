#!/bin/bash

echo "Build Android aab production"

# Clean previous builds
echo "Cleaning previous builds..."
flutter clean

# Get dependencies
echo "Getting dependencies..."
flutter pub get

# Build app bundle
echo "Building app bundle..."
flutter build appbundle --dart-define=environment=production --target-platform android-arm,android-arm64,android-x64

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "Build completed successfully!"
    echo "AAB file location:"
    find build/ -name "*.aab" -type f 2>/dev/null
else
    echo "Build failed!"
    exit 1
fi