-keep class androidx.appcompat.** { *; }
-keep class com.google.firebase.installations.** { *; }
-dontwarn com.google.firebase.**
-keep class io.flutter.plugins.firebase.* { *; }
-keep interface com.google.firebase.installations.** {*;}

-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }
-keep class io.grpc.** {*;}

-keep class com.google.android.play.** { *; }
-keep class com.google.android.gms.** { *; }
-keep class com.squareup.okhttp3.** { *; }
-keep class okhttp3.** { *; }
-keep class org.conscrypt.** { *; }
-keep class org.bouncycastle.** { *; }
-dontwarn org.bouncycastle.**
-dontwarn okhttp3.**
-dontwarn com.squareup.okhttp3.**
-dontwarn org.conscrypt.**

-keep class com.squareup.okhttp.** { *; }
-keep class io.grpc.** { *; }
-keep class io.grpc.okhttp.** { *; }
-dontwarn com.squareup.okhttp.**
-dontwarn io.grpc.**

-keep class com.google.android.play.core.tasks.** { *; }
-keep class com.google.android.play.core.splitcompat.** { *; }
-dontwarn com.google.android.play.core.tasks.**