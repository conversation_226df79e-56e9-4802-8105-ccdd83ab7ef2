plugins {
    id "com.android.application"
    id "dev.flutter.flutter-gradle-plugin"
    id "org.jetbrains.kotlin.android"
    id "com.google.gms.google-services"
    id "com.google.firebase.crashlytics"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def dartEnvironmentVariables = [
    ENVIRONMENT:'development',
    BUILD_VERSION:'v1.44.0'
]
if (project.hasProperty('dart-defines')) {
    dartEnvironmentVariables = dartEnvironmentVariables + project.property('dart-defines')
            .split(',')
            .collectEntries { entry ->
                def pair = new String(entry.decodeBase64(), 'UTF-8').split('=')
                [(pair.first().toUpperCase()): pair.last()]
            }
}

android {
    compileSdkVersion 34
    namespace "com.edison.parent"
    compileSdk = 35
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
        coreLibraryDesugaringEnabled true
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
        jvmTarget = '17'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    signingConfigs {
        release {
            storeFile file('edisonschool.keystore')
            storePassword 'Ntt@25102003'
            keyAlias 'trungtung'
            keyPassword 'Ntt@25102003'
        }
    }

    lintOptions { 
        checkReleaseBuilds false
    }

    defaultConfig {
        applicationId "com.edison.parent"
        minSdkVersion 23
        multiDexEnabled true
        targetSdkVersion 35
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
    }

    buildTypes {
        debug {
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            minifyEnabled false
            debuggable true
        }

        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            multiDexKeepProguard file('multidex-config.pro')
            ndk {
                abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86_64'
            }
        }
    }

    aaptOptions {
        noCompress 'tflite'
        ignoreAssetsPattern '!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~'
    }

    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }

    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/license.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/notice.txt'
        exclude 'META-INF/*.kotlin_module'
        exclude 'META-INF/proguard/androidx-annotations.pro'
        exclude 'META-INF/gradle/incremental.annotation.processors'
        exclude 'META-INF/INDEX.LIST'
        exclude 'META-INF/io.netty.versions.properties'
        exclude 'META-INF/com.google.android.play_core.version'
        exclude 'META-INF/play-feature-delivery_release.kotlin_module'
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
        pickFirst 'lib/arm64-v8a/libc++_shared.so'
        pickFirst 'lib/x86/libc++_shared.so'
        pickFirst 'lib/x86_64/libc++_shared.so'
        pickFirst 'META-INF/services/com.google.android.play.*'
        pickFirst 'META-INF/androidx.*'
    }

    dexOptions {
        javaMaxHeapSize "4g"
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'com.squareup.okhttp3:okhttp:4.9.1'
    implementation 'com.squareup.okhttp:okhttp:2.7.5'
    implementation 'com.google.android.gms:play-services-base:18.2.0'
    implementation platform('com.google.firebase:firebase-bom:32.7.0')
    implementation 'androidx.window:window:1.0.0'
    implementation 'androidx.window:window-java:1.0.0'
    implementation 'org.bouncycastle:bcprov-jdk15on:1.70'
    implementation 'org.conscrypt:conscrypt-android:2.5.2'
    implementation 'org.openjsse:openjsse:1.1.10'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'
    implementation 'com.google.android.play:feature-delivery:2.1.0'
    implementation 'com.google.android.play:app-update:2.1.0'
    implementation 'com.google.android.play:review:2.0.1'
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'io.grpc:grpc-okhttp:1.59.0'
}

configurations.all {
    resolutionStrategy {
        force 'com.google.android.play:feature-delivery:2.1.0'
        force 'com.google.android.play:app-update:2.1.0'
        force 'com.google.android.play:review:2.0.1'
    }
}
 